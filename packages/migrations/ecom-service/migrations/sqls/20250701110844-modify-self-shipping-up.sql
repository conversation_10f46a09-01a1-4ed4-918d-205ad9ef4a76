-- DROP existing shipping tables
DROP TABLE IF EXISTS main.weight_based_shipping_rules;
DROP TABLE IF EXISTS main.product_shipping_charges;
DROP TABLE IF EXISTS main.seller_shipping_charges;

ALTER TABLE main.seller_shipping_profiles
ADD COLUMN fallback_min_delivery_days INTEGER,
ADD COLUMN fallback_max_delivery_days INTEGER,
ADD COLUMN fallback_price NUMERIC(10,2);

CREATE TABLE IF NOT EXISTS main.seller_basic_shipping_charges (
    id              UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    shipping_profile_id UUID NOT NULL,
    state_type      VARCHAR(50) NOT NULL, -- 'IN_STATE' or 'OUT_STATE'
    price           NUMERIC(10,2) NOT NULL,
    is_active       BOOLEAN DEFAULT TRUE,
    created_on      TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         BOOLEAN DEFAULT FALSE,
    deleted_on      TIMESTAMPTZ,
    deleted_by      UUID,
    created_by      <PERSON>UI<PERSON>,
    modified_by     UUID,
    CONSTRAINT pk_seller_basic_shipping_charges PRIMARY KEY (id),
    CONSTRAINT fk_seller_basic_shipping_charges_profile FOREIGN KEY (shipping_profile_id) 
        REFERENCES main.seller_shipping_profiles(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS main.seller_advanced_shipping_charges (
    id              UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    shipping_profile_id UUID NOT NULL,
    state           VARCHAR(255) NOT NULL,
    min_weight_grams INTEGER NOT NULL,
    max_weight_grams INTEGER NOT NULL,
    price           NUMERIC(10,2) NOT NULL,
    is_active       BOOLEAN DEFAULT TRUE,
    created_on      TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         BOOLEAN DEFAULT FALSE,
    deleted_on      TIMESTAMPTZ,
    deleted_by      UUID,
    created_by      UUID,
    modified_by     UUID,
    CONSTRAINT pk_seller_advanced_shipping_charges PRIMARY KEY (id),
    CONSTRAINT fk_seller_advanced_shipping_charges_profile FOREIGN KEY (shipping_profile_id) 
        REFERENCES main.seller_shipping_profiles(id) ON DELETE CASCADE
);


CREATE TABLE IF NOT EXISTS main.seller_product_variant_shipping_charges (
    id              UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    shipping_profile_id UUID NOT NULL,
    product_variant_id UUID NOT NULL,
    state           VARCHAR(255) NOT NULL,
    price           NUMERIC(10,2) NOT NULL,
    is_active       BOOLEAN DEFAULT TRUE,
    created_on      TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted         BOOLEAN DEFAULT FALSE,
    deleted_on      TIMESTAMPTZ,
    deleted_by      UUID,
    created_by      UUID,
    modified_by     UUID,
    CONSTRAINT pk_seller_product_variant_shipping_charges PRIMARY KEY (id),
    CONSTRAINT fk_seller_product_variant_shipping_charges_profile FOREIGN KEY (shipping_profile_id) 
        REFERENCES main.seller_shipping_profiles(id) ON DELETE CASCADE,
    CONSTRAINT fk_seller_product_variant_shipping_charges_variant FOREIGN KEY (product_variant_id)
        REFERENCES main.product_variants(id) ON DELETE CASCADE
);

CREATE TRIGGER mdt_seller_basic_shipping_charges
BEFORE UPDATE ON main.seller_basic_shipping_charges
FOR EACH ROW
EXECUTE PROCEDURE main.moddatetime();

CREATE TRIGGER mdt_seller_advanced_shipping_charges
BEFORE UPDATE ON main.seller_advanced_shipping_charges
FOR EACH ROW
EXECUTE PROCEDURE main.moddatetime();

CREATE TRIGGER mdt_seller_product_variant_shipping_charges
BEFORE UPDATE ON main.seller_product_variant_shipping_charges
FOR EACH ROW
EXECUTE PROCEDURE main.moddatetime();

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_seller_basic_shipping_charges_profile_id ON main.seller_basic_shipping_charges(shipping_profile_id);
CREATE INDEX IF NOT EXISTS idx_seller_advanced_shipping_charges_profile_id ON main.seller_advanced_shipping_charges(shipping_profile_id);
CREATE INDEX IF NOT EXISTS idx_seller_product_variant_shipping_charges_profile_id ON main.seller_product_variant_shipping_charges(shipping_profile_id);
