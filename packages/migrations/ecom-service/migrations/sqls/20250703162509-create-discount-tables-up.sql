/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.discounts (
    id uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    start_date TIMESTAMPTZ,
    end_date TIMESTAMPTZ,
    usage_limit_per_user INTEGER DEFAULT 1,
    combinable BOOLEAN DEFAULT FALSE,
    created_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on    timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by     uuid,
    modified_by    uuid,
    deleted        boolean DEFAULT false NOT NULL,
    deleted_by     uuid,
    deleted_on     timestamptz,
    CONSTRAINT pk_discounts PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS main.discount_conditions (
    id uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    discount_id UUID NOT NULL,
    threshold_amount NUMERIC NOT NULL,
    discount_type VARCHAR(10) NOT NULL,
    discount_value NUMERIC NOT NULL,
    condition_type VARCHAR(20) NOT NULL,
    is_app_only BOOLEAN DEFAULT FALSE,
    created_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on    timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by     uuid,
    modified_by    uuid,
    deleted        boolean DEFAULT false NOT NULL,
    deleted_by     uuid,
    deleted_on     timestamptz,
    CONSTRAINT pk_discount_conditions PRIMARY KEY (id),
    CONSTRAINT fk_discount_conditions_discount_id FOREIGN KEY (discount_id)
        REFERENCES main.discounts(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS main.discount_usages (
    id uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    user_id UUID NOT NULL,
    discount_id UUID NOT NULL,
    order_id UUID NOT NULL,
    used_on TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on    timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by     uuid,
    modified_by    uuid,
    deleted        boolean DEFAULT false NOT NULL,
    deleted_by     uuid,
    deleted_on     timestamptz,
    CONSTRAINT pk_discount_usages PRIMARY KEY (id),
    CONSTRAINT fk_discount_usages_discount_id FOREIGN KEY (discount_id)
        REFERENCES main.discounts(id) ON DELETE CASCADE
);
