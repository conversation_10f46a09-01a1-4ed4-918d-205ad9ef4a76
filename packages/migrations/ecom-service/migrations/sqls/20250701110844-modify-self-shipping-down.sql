

-- Create seller shipping charges table for state-to-state shipping
CREATE TABLE IF NOT EXISTS main.seller_shipping_charges (
    id                      UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    shipping_profile_id     UUID NOT NULL,
    country_code            VARCHAR(10) NOT NULL,
    state_code              VARCHAR(10),
    is_default              BOOLEAN DEFAULT FALSE,
    base_charge             NUMERIC(10, 2) NOT NULL,
    additional_charge       NUMERIC(10, 2) DEFAULT 0,
    free_shipping_threshold NUMERIC(10, 2),
    estimated_days_min      INTEGER,
    estimated_days_max      INTEGER,
    created_on              TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on             TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                 BOOLEAN DEFAULT FALSE,
    deleted_on              TIMESTAMPTZ,
    deleted_by              <PERSON><PERSON><PERSON>,
    created_by              <PERSON><PERSON><PERSON>,
    modified_by             <PERSON><PERSON><PERSON>,
    CONSTRAINT pk_seller_shipping_charges PRIMARY KEY (id),
    CONSTRAINT fk_seller_shipping_charges_profile FOREIGN KEY (shipping_profile_id) 
        REFERENCES main.seller_shipping_profiles(id) ON DELETE CASCADE
);

-- Create product-specific shipping charges table
CREATE TABLE IF NOT EXISTS main.product_shipping_charges (
    id                      UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    product_id              UUID NOT NULL,
    product_variant_id      UUID,
    shipping_profile_id     UUID NOT NULL,
    override_default        BOOLEAN DEFAULT TRUE,
    base_charge             NUMERIC(10, 2) NOT NULL,
    additional_charge       NUMERIC(10, 2) DEFAULT 0,
    free_shipping_threshold NUMERIC(10, 2),
    created_on              TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on             TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                 BOOLEAN DEFAULT FALSE,
    deleted_on              TIMESTAMPTZ,
    deleted_by              UUID,
    created_by              UUID,
    modified_by             UUID,
    CONSTRAINT pk_product_shipping_charges PRIMARY KEY (id),
    CONSTRAINT fk_product_shipping_charges_product FOREIGN KEY (product_id) 
        REFERENCES main.products(id) ON DELETE CASCADE,
    CONSTRAINT fk_product_shipping_charges_variant FOREIGN KEY (product_variant_id) 
        REFERENCES main.product_variants(id) ON DELETE CASCADE,
    CONSTRAINT fk_product_shipping_charges_profile FOREIGN KEY (shipping_profile_id) 
        REFERENCES main.seller_shipping_profiles(id) ON DELETE CASCADE
);

-- Create weight-based shipping rules table
CREATE TABLE IF NOT EXISTS main.weight_based_shipping_rules (
    id                      UUID DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    shipping_profile_id     UUID NOT NULL,
    min_weight              NUMERIC(10, 2) NOT NULL,
    max_weight              NUMERIC(10, 2),
    charge                  NUMERIC(10, 2) NOT NULL,
    created_on              TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on             TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                 BOOLEAN DEFAULT FALSE,
    deleted_on              TIMESTAMPTZ,
    deleted_by              UUID,
    created_by              UUID,
    modified_by             UUID,
    CONSTRAINT pk_weight_based_shipping_rules PRIMARY KEY (id),
    CONSTRAINT fk_weight_based_shipping_rules_profile FOREIGN KEY (shipping_profile_id) 
        REFERENCES main.seller_shipping_profiles(id) ON DELETE CASCADE
);

DROP TABLE IF EXISTS main.seller_basic_shipping_charges CASCADE;
DROP TABLE IF EXISTS main.seller_advanced_shipping_charges CASCADE;
DROP TABLE IF EXISTS main.seller_product_variant_shipping_charges CASCADE;