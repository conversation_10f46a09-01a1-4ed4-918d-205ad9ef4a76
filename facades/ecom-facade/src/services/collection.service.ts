import {injectable, BindingScope, inject, Getter} from '@loopback/core';
import {service} from '@loopback/core';
import {AssetService} from './asset.service';
import {
  Collection,
  CollectionWithRelations,
  IAuthUserWithTenant,
  Profile,
} from '../models';
import {AuthenticationBindings} from 'loopback4-authentication';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {CollectionStatus} from '../enums/collection-status.enum';
import {NotificationHelperService} from './notification-helper.service';
import {ProfileProxyType} from '../datasources/configs/profile-proxy.config';
import {Request, RestBindings, Response, HttpErrors} from '@loopback/rest';

interface EmailData {
  productName: string;
  supportId: string;
  brand: string;
  [key: string]: string;
}

@injectable({scope: BindingScope.TRANSIENT})
export class CollectionService {
  private token: string;
  constructor(
    @service(AssetService)
    private readonly assetService: AssetService,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @restService(Collection)
    private readonly collectionProxy: ModifiedRestService<Collection>,
    @inject(RestBindings.Http.RESPONSE)
    private readonly response: Response,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @service(NotificationHelperService)
    private readonly notificationHelper: NotificationHelperService,
    @restService(Profile)
    private readonly profileProxyConfig: ProfileProxyType,
  ) {
    if (request.headers.authorization) {
      this.token = request.headers.authorization;
    }
  }

  async getCollectionWithPresignedUrl(
    collection: CollectionWithRelations,
  ): Promise<CollectionWithRelations> {
    if (collection.featuredAssetId && collection?.featuredAsset?.preview) {
      collection.featuredAsset = this.assetService.getAssetWithPreview(
        collection.featuredAsset,
      );
    }
    return collection;
  }

  async createCollectionWithCurrentUser(
    data: Omit<Collection, 'id' | 'status'>,
  ): Promise<Collection> {
    const currentUser = await this.getCurrentUser();
    if (!currentUser?.id || !currentUser?.role?.length) {
      throw new HttpErrors.Unauthorized('User info missing');
    }

    // Check for existing collection with same name (not deleted)
    const existing = await this.collectionProxy.find({
      where: {
        name: data.name,
        deleted: false,
      },
    });

    if (existing.length > 0) {
      throw new HttpErrors.BadRequest(
        `Collection with name "${data.name}" already exists`,
      );
    }

    const role = currentUser.role[0].toLowerCase();
    const status = this.mapRoleToStatus(role);

    return this.collectionProxy.create({
      ...data,
      status,
    });
  }

  private mapRoleToStatus(role: string): CollectionStatus {
    switch (role) {
      case 'Seller':
      case 's':
        return CollectionStatus.PENDING;
      case 'Admin':
      default:
        return CollectionStatus.ACTIVE;
    }
  }

  async updateCollectionStatusById(
    id: string,
    updatePayload: Pick<Collection, 'status'>,
  ): Promise<void> {
    const collection = await this.collectionProxy
      .findById(id)
      .catch(() => null);
    if (!collection) {
      throw new HttpErrors.NotFound(`Collection with ID ${id} not found`);
    }

    if (!collection.createdBy) {
      throw new HttpErrors.BadRequest('Collection has no linked seller');
    }

    if (!this.token) {
      throw new HttpErrors.Unauthorized('Missing token for user service call');
    }

    const user = await this.profileProxyConfig.getUserTenantById(
      collection.createdBy,
      this.token,
      {include: [{relation: 'user'}]},
    );
    await this.collectionProxy.updateById(id, updatePayload);

    const emailData: EmailData = {
      productName: 'Ecomdukes',
      supportId: '<EMAIL>',
      brand: 'Ecomdukes',
      collectionName: collection.name,
    };

    let templateName = '';
    let subject = '';

    if (updatePayload.status === CollectionStatus.ACTIVE) {
      templateName = 'collection-approved-email.hbs';
      subject = 'Your Collection Has Been Approved!';
    } else if (updatePayload.status === CollectionStatus.REJECTED) {
      templateName = 'collection-rejected-email.hbs';
      subject = 'Your Collection Has Been Rejected';
    } else {
      return;
    }

    const formattedEmailData: {[key: string]: string} = Object.fromEntries(
      Object.entries(emailData).filter(([_, v]) => v !== undefined),
    );

    await this.notificationHelper.sendEmail(
      templateName,
      subject,
      formattedEmailData,
      user.user?.email ?? '',
      'Seller',
    );
  }
}
