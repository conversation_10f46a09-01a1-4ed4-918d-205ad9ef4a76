import {injectable, BindingScope} from '@loopback/core';
import {restService} from '@sourceloop/core';
import {TopicProxyType} from '../datasources/configs/topic-proxy.config';
import {Topic} from '../models';

@injectable({scope: BindingScope.TRANSIENT})
export class TopicService {
  constructor(
    @restService(Topic)
    private readonly topicProxy: TopicProxyType,
  ) {}

  async subscribeToFcmTopic(
    topic: string,
    fcmToken: string,
  ): Promise<{success: boolean; message: string}> {
    const token = process.env.NOTIFICATION_SERVICE_TOKEN;
    return this.topicProxy.subscribeToFcmTopic(
      topic,
      fcmToken,
      `Bearer ${token}`,
    );
  }
}
