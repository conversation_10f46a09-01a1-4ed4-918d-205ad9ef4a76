import {
  injectable,
  /* inject, */ BindingScope,
  inject,
  Getter,
} from '@loopback/core';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {Customer, Discount, IAuthUserWithTenant} from '../models';
import {DiscountProxyType} from '../datasources/configs/discount-proxy.config';
import {CustomerStatus} from '@local/core';
import {HttpErrors} from '@loopback/rest';
import {AuthenticationBindings} from 'loopback4-authentication';
import {repository} from '@loopback/repository';
import {CustomerRepository} from '../repositories';

@injectable({scope: BindingScope.TRANSIENT})
export class DiscountHelperService {
  constructor(
    @restService(Discount)
    private discountProxy: DiscountProxyType,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @repository(CustomerRepository)
    private readonly customerRepository: CustomerRepository,
    @restService(Customer)
    public customerProxy: ModifiedRestService<Customer>,
  ) {}

  async checkDiscountEligibility(
    body: {
      cartTotal: number;
      isFromApp: boolean;
    },
    token: string,
  ) {
    const customer = await this.getCustomer();
    return this.discountProxy.getEligibleFirstOrderDiscount(
      {...body, customerId: customer.id},
      token,
    );
  }

  async getCustomer(): Promise<Customer> {
    const user = await this.getCurrentUser();
    let customer = await this.customerRepository.get(user.userTenantId ?? '');
    if (!customer) {
      const customers = await this.customerProxy.find({
        where: {
          userTenantId: user.userTenantId ?? '',
          status: CustomerStatus.ACTIVE,
        },
        limit: 1,
      });

      if (!customers?.length) {
        throw new HttpErrors.BadRequest(
          `Your account is either suspended or cannot be found in our system. Please contact support.`,
        );
      }
      customer = customers[0];
      await this.customerRepository.set(user.userTenantId ?? '', customer);
    }
    return customer;
  }
}
