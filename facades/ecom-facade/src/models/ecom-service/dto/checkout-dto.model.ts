import {Model, model, property} from '@loopback/repository';

@model()
export class CheckoutDto extends Model {
  @property({
    type: 'string',
    required: true,
  })
  shippingAddressId: string;

  @property({
    type: 'string',
    required: true,
  })
  billingAddressId: string;

  @property({
    type: 'number',
    required: false,
  })
  ecomDukeCoinsApplied?: number;

  @property({type: 'boolean', required: false})
  useDukeCoins?: boolean;

  @property({
    type: 'string',
  })
  discountConditionId?: string;

  @property({
    type: 'string',
    required: false,
  })
  gstNumber?: string;

  @property({
    type: 'string',
    required: false,
  })
  businessName?: string;

  @property({
    type: 'string',
    required: false,
  })
  businessAddress?: string;

  @property({
    type: 'string',
    required: false,
  })
  paymentMethod?: string;

  constructor(data?: Partial<CheckoutDto>) {
    super(data);
  }
}
