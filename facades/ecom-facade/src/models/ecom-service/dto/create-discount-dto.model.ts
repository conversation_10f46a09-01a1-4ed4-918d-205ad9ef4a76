import {model, property} from '@loopback/repository';
import {DiscountCondition} from '../discount-condition.model';
import {Discount} from '../discount.model';

@model()
export class CreateDiscountDto extends Discount {
  @property({
    type: 'array',
    itemType: 'object',
    required: true,
  })
  conditions: Omit<DiscountCondition, 'id'>[];

  constructor(data?: Partial<CreateDiscountDto>) {
    super(data);
  }
}
