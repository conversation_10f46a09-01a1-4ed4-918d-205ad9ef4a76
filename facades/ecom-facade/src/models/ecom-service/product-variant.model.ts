import {
  model,
  property,
  belongsTo,
  hasMany,
  hasOne,
} from '@loopback/repository';
import {Product, ProductWithRelations} from './product.model';
import {Asset} from './asset.model';
import {TaxCategory} from './tax-category.model';
import {UserModifiableEntity} from '@sourceloop/core';
import {ProductSpecification} from './product-speicification.model';
import {ProductDetail} from './product-detail.model';
import {ProductMoreInfo} from './product-more-info.model';
import {ProductDisclaimer} from './product-disclaimer.model';
import {ProductReturnPolicy} from './product-return-policy.model';
import {ProductSuitability} from './product-suitability.model';
import {ProductTermsAndCondition} from './product-terms-and-condition.model';
import {ProductBoxContent} from './product-box-content.model';
import {ProductUniqueness} from './product-uniqueness.model';
import {ProductPersonalWork} from './product-personal-work.model';
import {ProductCustomizationField} from './product-customization-field.model';
import {AssetResponseDto} from './asset-response-dto.model';
import {ProductVariantPrice} from './product-variant-price.model';
import {ProductFacetValueWithRelations} from './product-facet-value.model';
import {ProductVariantAsset} from './product-variant-asset.model';
import {ProductVariantFacetValue} from './product-variant-facet-value.model';
import {Seller} from '../auth-service';
import {
  ProductVariantOption,
  ProductVariantOptionWithRelations,
} from './product-variant-option.model';
import {Wishlist} from './wishlist.model';
import {Review, ReviewWithRelations} from './review.model';

@model({name: 'product_variants'})
export class ProductVariant extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'boolean',
    default: true,
  })
  enabled?: boolean;

  @property({
    type: 'string',
    required: true,
  })
  sku: string;

  @property({
    type: 'number',
    default: 0,
    name: 'out_of_stock_threshold',
  })
  outOfStockThreshold?: number;

  @property({
    type: 'string',
    name: 'track_inventory',
  })
  trackInventory?: string;

  @property({
    type: 'string',
    name: 'zoho_item_id',
  })
  zohoItemId?: string;

  @belongsTo(() => Product, {keyTo: 'id'}, {name: 'product_id'})
  productId: string;

  @belongsTo(() => Asset, {keyTo: 'id'}, {name: 'featured_asset_id'})
  featuredAssetId: string;

  @belongsTo(() => TaxCategory, {keyTo: 'id'}, {name: 'tax_category_id'})
  taxCategoryId: string;

  @hasMany(() => ProductSpecification, {keyTo: 'productVariantId'})
  productSpecifications: ProductSpecification[];

  @hasOne(() => ProductDetail, {keyTo: 'productVariantId'})
  productDetail: ProductDetail;

  @hasOne(() => ProductMoreInfo, {keyTo: 'productVariantId'})
  productMoreInfo: ProductMoreInfo;

  @hasOne(() => ProductDisclaimer, {keyTo: 'productVariantId'})
  productDisclaimer: ProductDisclaimer;

  @hasOne(() => ProductReturnPolicy, {keyTo: 'productVariantId'})
  productReturnPolicy: ProductReturnPolicy;

  @hasOne(() => ProductSuitability, {keyTo: 'productVariantId'})
  productSuitability: ProductSuitability;

  @hasOne(() => ProductTermsAndCondition, {keyTo: 'productVariantId'})
  productTermsAndCondition: ProductTermsAndCondition;

  @hasMany(() => ProductBoxContent, {keyTo: 'productVariantId'})
  productBoxContents: ProductBoxContent[];

  @hasOne(() => ProductUniqueness, {keyTo: 'productVariantId'})
  productUniqueness: ProductUniqueness;

  @hasOne(() => ProductPersonalWork, {keyTo: 'productVariantId'})
  productPersonalWork: ProductPersonalWork;

  @hasMany(() => ProductCustomizationField, {keyTo: 'productVariantId'})
  productCustomizationFields: ProductCustomizationField[];

  @hasOne(() => ProductVariantPrice, {keyTo: 'productVariantId'})
  productVariantPrice: ProductVariantPrice;

  @hasMany(() => ProductVariantAsset, {keyTo: 'productVariantId'})
  productVariantAssets: ProductVariantAsset[];

  @hasMany(() => ProductVariantFacetValue, {keyTo: 'productVariantId'})
  productVariantFacetValues: ProductVariantFacetValue[];

  @hasMany(() => ProductVariantOption, {keyTo: 'productVariantId'})
  productVariantOptions: ProductVariantOption[];

  @hasOne(() => Wishlist, {keyTo: 'productVariantId'})
  wishlist: Wishlist;

  @hasMany(() => Review, {keyTo: 'productVariantId'})
  reviews: Review[];

  constructor(data?: Partial<ProductVariant>) {
    super(data);
  }
}

export interface ProductVariantRelations {
  featuredAsset: AssetResponseDto;
  product: ProductWithRelations;
  productFacetValues: ProductFacetValueWithRelations[];
  productVariantPrice: ProductVariantPrice;
  seller: Seller;
  review?: ReviewWithRelations;
  productVariantOptions?: ProductVariantOptionWithRelations[];
}

export type ProductVariantWithRelations = ProductVariant &
  ProductVariantRelations;
