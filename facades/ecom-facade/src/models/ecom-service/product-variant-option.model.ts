import {model, property, belongsTo, Entity} from '@loopback/repository';
import {ProductVariant} from './product-variant.model';
import {
  ProductOption,
  ProductOptionWithRelations,
} from './product-option.model';

@model({name: 'product_variant_options'})
export class ProductVariantOption extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @belongsTo(() => ProductVariant, {keyTo: 'id'}, {name: 'product_variant_id'})
  productVariantId: string;

  @belongsTo(() => ProductOption, {keyTo: 'id'}, {name: 'product_option_id'})
  productOptionId: string;

  constructor(data?: Partial<ProductVariantOption>) {
    super(data);
  }
}

export interface ProductVariantOptionRelations {
  productOption?: ProductOptionWithRelations;
}

export type ProductVariantOptionWithRelations = ProductVariantOption &
  ProductVariantOptionRelations;
