import {model, property, belongsTo, hasMany} from '@loopback/repository';
import {Cart} from './cart.model';
import {OrderStatus, PaymentMethod} from '@local/core';
import {PromoCode} from './promo-code.model';
import {OrderLineItem} from './order-line-item.model';
import {Customer, CustomerWithRelations} from '../auth-service';
import {Address} from './address.model';
import {UserModifiableEntity} from '@sourceloop/core';
import {DiscountCondition} from './discount-condition.model';

@model({name: 'orders'})
export class Order extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
    required: false,
    name: 'order_id',
  })
  orderId: string;

  @property({
    type: 'string',
    required: false,
    name: 'order_reference_id',
  })
  orderReferenceId: string;

  @property({
    type: 'number',
    required: true,
    name: 'total_amount',
  })
  totalAmount: number;

  @property({
    type: 'number',
    name: 'discount_amount',
  })
  discountAmount?: number;

  @property({
    type: 'number',
    name: 'payable_amount',
  })
  payableAmount?: number;

  @property({
    type: 'string',
    required: true,
  })
  currency: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(OrderStatus),
    },
  })
  status: string;

  @property({
    type: 'string',
    name: 'invoice_id',
  })
  invoiceId: string;

  @property({
    type: 'number',
    name: 'ecom_duke_coins_used',
    default: 0,
  })
  ecomDukeCoinsUsed?: number;

  @property({
    type: 'string',
    name: 'invoice_url',
  })
  invoiceUrl: string;

  @property({
    type: 'string',
    name: 'gst_number',
  })
  gstNumber?: string;

  @property({
    type: 'string',
    name: 'business_name',
  })
  businessName?: string;

  @property({
    type: 'string',
    name: 'payment_method',
  })
  paymentMethod?: PaymentMethod;

  @property({
    type: 'string',
    name: 'business_address',
  })
  businessAddress?: string;

  @belongsTo(() => Address, {keyTo: 'id'}, {name: 'shipping_address_id'})
  shippingAddressId: string;

  @belongsTo(() => Address, {keyTo: 'id'}, {name: 'billing_address_id'})
  billingAddressId: string;

  @belongsTo(
    () => Customer,
    {keyTo: 'id'},
    {name: 'customer_id', required: true},
  )
  customerId: string;

  @belongsTo(() => Cart)
  cartId: string;

  @belongsTo(
    () => DiscountCondition,
    {keyTo: 'id'},
    {name: 'discount_condition_id'},
  )
  discountConditionId: string;

  @belongsTo(() => PromoCode, {keyTo: 'id'}, {name: 'promo_code_id'})
  promoCodeId: string;

  @hasMany(() => OrderLineItem, {keyTo: 'orderId'})
  orderLineItems: OrderLineItem[];

  constructor(data?: Partial<Order>) {
    super(data);
  }
}

export interface OrderRelations {
  customer?: CustomerWithRelations;
  billingAddress?: Address;
  shippingAddress?: Address;
  // describe navigational properties here
}

export type OrderWithRelations = Order & OrderRelations;
