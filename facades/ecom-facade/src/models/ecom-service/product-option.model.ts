import {model, property, belongsTo} from '@loopback/repository';
import {
  ProductOptionGroup,
  ProductOptionGroupWithRelations,
} from './product-option-group.model';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'product_option'})
export class ProductOption extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  code: string;

  @belongsTo(
    () => ProductOptionGroup,
    {keyTo: 'id'},
    {name: 'product_option_group_id'},
  )
  productOptionGroupId: string;

  constructor(data?: Partial<ProductOption>) {
    super(data);
  }
}

export interface ProductOptionRelations {
  productOptionGroup?: ProductOptionGroupWithRelations;
}

export type ProductOptionWithRelations = ProductOption & ProductOptionRelations;
