import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  response,
  RestBindings,
} from '@loopback/rest';
import {inject} from '@loopback/core';
import {Request} from 'express';

import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {
  STATUS_CODE,
  CONTENT_TYPE,
  restService,
  ModifiedRestService,
} from '@sourceloop/core';

import {SellerBasicShippingCharge} from '../models';

const basePath = '/basic-shipping-charges';

export class SellerBasicShippingChargeController {
  private token: string;

  constructor(
    @restService(SellerBasicShippingCharge)
    private readonly proxyService: ModifiedRestService<SellerBasicShippingCharge>,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'SellerBasicShippingCharge model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerBasicShippingCharge),
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerBasicShippingCharge, {
            title: 'NewSellerBasicShippingCharge',
          }),
        },
      },
    })
    sellerBasicShippingCharge: SellerBasicShippingCharge,
  ): Promise<SellerBasicShippingCharge> {
    return this.proxyService.create(sellerBasicShippingCharge, this.token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'SellerBasicShippingCharge count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(SellerBasicShippingCharge)
    where?: Where<SellerBasicShippingCharge>,
  ): Promise<Count> {
    return this.proxyService.count(where, this.token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of SellerBasicShippingCharge instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SellerBasicShippingCharge, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(SellerBasicShippingCharge)
    filter?: Filter<SellerBasicShippingCharge>,
  ): Promise<SellerBasicShippingCharge[]> {
    return this.proxyService.find(filter, this.token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'SellerBasicShippingCharge instance with relations',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerBasicShippingCharge, {
          includeRelations: true,
        }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(SellerBasicShippingCharge, {exclude: 'where'})
    filter?: FilterExcludingWhere<SellerBasicShippingCharge>,
  ): Promise<SellerBasicShippingCharge> {
    return this.proxyService.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'SellerBasicShippingCharge PATCH success',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerBasicShippingCharge, {
          includeRelations: true,
        }),
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerBasicShippingCharge, {
            partial: true,
          }),
        },
      },
    })
    dto: Partial<SellerBasicShippingCharge>,
  ): Promise<void> {
    return this.proxyService.updateById(id, dto);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerBasicShippingCharge DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.proxyService.deleteById(id, this.token);
  }
}
