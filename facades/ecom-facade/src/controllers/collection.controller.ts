import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
} from '@loopback/rest';
import {Collection, CollectionWithRelations} from '../models';
import {
  CONTENT_TYPE,
  ModifiedRestService,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {service} from '@loopback/core';
import {CollectionService} from '../services';
const basePath = '/collections';
export class CollectionController {
  constructor(
    @restService(Collection)
    private readonly collectionproxy: ModifiedRestService<Collection>,
    @service(CollectionService)
    private readonly collectionService: CollectionService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCollection]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Collection model instance',
        content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Collection)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Collection, {
            title: 'NewCollection',
            exclude: ['id'],
          }),
        },
      },
    })
    collection: Omit<Collection, 'id'>,
  ): Promise<Collection> {
    return this.collectionService.createCollectionWithCurrentUser(collection);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCollection]})
  @get(`${basePath}/count`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Collection model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(Collection) where?: Where<Collection>,
  ): Promise<Count> {
    return this.collectionproxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCollection]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Collection model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Collection, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(Collection) filter?: Filter<Collection>,
  ): Promise<Collection[]> {
    const collections = (await this.collectionproxy.find(
      filter,
    )) as CollectionWithRelations[];
    return Promise.all(
      collections.map(collection =>
        this.collectionService.getCollectionWithPresignedUrl(collection),
      ),
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCollection]})
  @get(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Collection model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(Collection, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Collection, {exclude: 'where'})
    filter?: FilterExcludingWhere<Collection>,
  ): Promise<Collection> {
    const collection = (await this.collectionproxy.findById(
      id,
      filter,
    )) as CollectionWithRelations;
    return this.collectionService.getCollectionWithPresignedUrl(collection);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCollection]})
  @patch(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Collection PATCH success',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Collection, {partial: true}),
        },
      },
    })
    collection: Collection,
  ): Promise<void> {
    await this.collectionService.updateCollectionStatusById(id, collection);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteCollection]})
  @del(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Collection DELETE success',
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.collectionproxy.deleteById(id);
  }
}
