import {AnyObject} from '@loopback/repository';
import {getModelSchemaRef, param, post, requestBody} from '@loopback/rest';
import {
  CONTENT_TYPE,
  ErrorCodes,
  OPERATION_SECURITY_SPEC,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';
import {authorize} from 'loopback4-authorization';
import {
  AuthUser,
  LocalUserProfileDto,
} from '@sourceloop/authentication-service';
import {SignupRequestDto} from '@sourceloop/authentication-service';
import {SignupWithTokenReponseDto} from '@sourceloop/authentication-service';
import {AuthProxyType} from '../datasources/configs';
import {SignupService} from '../services/signup.service';
import {service} from '@loopback/core';

const successResponse = 'Success Response.';
const basePath = '/auth/sign-up';

export class SignupRequestController {
  constructor(
    @restService(AuthUser)
    private readonly authProvider: AuthProxyType,
    @service(SignupService)
    private readonly signupService: SignupService,
  ) {}

  @authorize({permissions: ['*']})
  @post(`${basePath}/create-token`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: successResponse,
      },
      ...ErrorCodes,
    },
  })
  async requestSignup(
    @requestBody()
    signUpRequest: SignupRequestDto<LocalUserProfileDto>,
  ): Promise<void> {
    return this.authProvider.requestSignup(signUpRequest);
  }

  @authorize({permissions: ['*']})
  @post(`${basePath}/create-user`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: successResponse,
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(LocalUserProfileDto),
          },
        },
      },
      ...ErrorCodes,
    },
  })
  async signupWithToken(
    @requestBody()
    req: LocalUserProfileDto & {
      subscribeToNewsletter?: boolean;
      fcmToken?: string;
    },
    @param.header.string('Authorization') token: string,
    @param.header.string('x-origin') xOrigin: string,
  ): Promise<SignupWithTokenReponseDto<AnyObject>> {
    return this.signupService.signupWithReferralAndToken(req, xOrigin, token);
  }
}
