import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  RestBindings,
  Request,
} from '@loopback/rest';
import {Seller, SellerStore} from '../models';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {CONTENT_TYPE, restService, STATUS_CODE} from '@sourceloop/core';
import {PermissionKeys} from '@local/core';
import {SellerProxyType, SellerStoreProxyType} from '../datasources/configs';
import {inject} from '@loopback/context';
import {service} from '@loopback/core';
import {SellerService} from '../services';
import {SellerStoreExtendedService} from '../services/seller-store.service';

const basePath = '/seller-stores';
export class SellerStoreController {
  private token: string;
  constructor(
    @restService(SellerStore)
    public sellerStoreProxyService: SellerStoreProxyType,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @restService(Seller)
    private readonly sellerProxySerive: SellerProxyType,
    @service(SellerService)
    public sellerService: SellerService,
    @service(SellerStoreExtendedService)
    private readonly sellerStoreService: SellerStoreExtendedService,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'SellerStore model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerStore, {exclude: ['id']}),
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          title: 'NewSellerStore',
          schema: getModelSchemaRef(SellerStore),
        },
      },
    })
    sellerStore: SellerStore,
  ): Promise<SellerStore> {
    return this.sellerStoreProxyService.create(sellerStore);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'SellerStore model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(SellerStore) where?: Where<SellerStore>,
  ): Promise<Count> {
    return this.sellerStoreProxyService.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of SellerStore model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SellerStore, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SellerStore) filter?: Filter<SellerStore>,
  ): Promise<SellerStore[]> {
    return this.sellerStoreProxyService.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: ['*']})
  @get(`${basePath}/seller/{sellerId}`)
  @response(STATUS_CODE.OK, {
    description: 'SellerStore model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerStore, {includeRelations: true}),
      },
    },
  })
  async findBySellerId(
    @param.path.string('sellerId') sellerId: string,
    @param.filter(SellerStore, {exclude: 'where'})
    filter?: FilterExcludingWhere<SellerStore>,
  ): Promise<SellerStore | null> {
    return this.sellerStoreService.getStoreWithUserDetails(
      this.token,
      sellerId,
      filter,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'SellerStore model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerStore, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(SellerStore, {exclude: 'where'})
    filter?: FilterExcludingWhere<SellerStore>,
  ): Promise<SellerStore> {
    return this.sellerStoreProxyService.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerStore PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody.file()
    _req: Request,
  ): Promise<void> {
    await this.sellerService.updateSellerStoreById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerStore PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() sellerStore: SellerStore,
  ): Promise<void> {
    await this.sellerStoreProxyService.replaceById(id, sellerStore);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerStore DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.sellerStoreProxyService.deleteById(id);
  }
}
