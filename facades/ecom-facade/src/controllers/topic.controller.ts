import {inject} from '@loopback/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {
  post,
  requestBody,
  response,
  RestBindings,
  Request,
} from '@loopback/rest';
import {CONTENT_TYPE, restService, STATUS_CODE} from '@sourceloop/core';
import {Topic} from '../models';
import {TopicProxyType} from '../datasources/configs/topic-proxy.config';

const basePath = '/topics';

export class TopicController {
  private token: string;
  constructor(
    @restService(Topic)
    private readonly topicService: TopicProxyType,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {
    if (request.headers.authorization) {
      this.token = request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({
    permissions: [
      PermissionKeys.ViewConfiguration,
      PermissionKeys.CreateConfiguration,
    ],
  })
  @post(`${basePath}/subscribe-fcm`)
  @response(STATUS_CODE.OK, {
    description: 'Subscribe FCM token to topic',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'object',
          properties: {
            success: {type: 'boolean'},
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async subscribeToFcmTopic(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
            required: ['topic', 'fcmToken'],
            properties: {
              topic: {type: 'string'},
              fcmToken: {type: 'string'},
            },
          },
        },
      },
    })
    payload: {
      topic: string;
      fcmToken: string;
    },
  ): Promise<{success: boolean; message: string}> {
    const {topic, fcmToken} = payload;
    return this.topicService.subscribeToFcmTopic(topic, fcmToken, this.token);
  }
}
