import {RestOperationTemplate} from '@sourceloop/core';

export type TopicProxyType = {
  subscribeToFcmTopic(
    topic: string,
    fcmToken: string,
    token: string,
  ): Promise<{success: boolean; message: string}>;
};

export const TopicProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: `/topics/subscribe-fcm`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: {
        topic: '{topic}',
        fcmToken: '{fcmToken}',
      },
      query: {},
    },
    functions: {
      subscribeToFcmTopic: ['topic', 'fcmToken', 'token'],
    },
  },
];
