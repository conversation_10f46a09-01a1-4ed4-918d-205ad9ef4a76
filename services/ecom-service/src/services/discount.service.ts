import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
  DiscountConditionRepository,
  DiscountRepository,
  OrderRepository,
} from '../repositories';
import {CreateDiscountDto, Discount, DiscountCondition} from '../models';

interface EligibleDiscountResult {
  isEligible: boolean;
  appliedDiscount: {
    conditionId: string;
    discountValue: number;
    discountType: 'FLAT' | 'PERCENT';
  } | null;
  nextTierMessage?: string;
  discountBreakdownMessage?: string;
}

@injectable({scope: BindingScope.TRANSIENT})
export class DiscountService {
  constructor(
    @repository(DiscountRepository)
    private discountRepo: DiscountRepository,
    @repository(DiscountConditionRepository)
    private discountConditionRepo: DiscountConditionRepository,
    @repository(OrderRepository)
    public orderRepo: OrderRepository,
  ) {}

  conditions: Omit<DiscountCondition, 'id'>[];

  async createDiscountWithConditions(
    dto: CreateDiscountDto,
  ): Promise<Discount> {
    const {conditions, ...discount} = dto;
    const transaction = await this.discountRepo.dataSource.beginTransaction();
    try {
      const createdDiscount = await this.discountRepo.create(discount, {
        transaction,
      });

      const conditionsWithDiscountId = conditions.map(condition => ({
        ...condition,
        discountId: createdDiscount.id,
      }));

      await this.discountConditionRepo.createAll(conditionsWithDiscountId, {
        transaction,
      });
      await transaction.commit();
      return createdDiscount;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async updateDiscountWithConditions(
    id: string,
    dto: CreateDiscountDto,
  ): Promise<void> {
    const {conditions, ...discountData} = dto;
    const transaction = await this.discountRepo.dataSource.beginTransaction();
    try {
      await this.discountRepo.updateById(id, discountData, {transaction});
      await this.discountConditionRepo.deleteAllHard(
        {discountId: id},
        {transaction},
      );
      const conditionsWithDiscountId = conditions.map(condition => ({
        ...condition,
        discountId: id,
      }));
      await this.discountConditionRepo.createAll(conditionsWithDiscountId, {
        transaction,
      });
      await transaction.commit();
      return;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async getEligibleFirstOrderDiscount(params: {
    customerId: string;
    cartTotal: number;
    isFromApp: boolean;
  }): Promise<EligibleDiscountResult> {
    const {customerId, cartTotal, isFromApp} = params;

    // 1. Check if user has any successful orders
    const existingOrders = await this.orderRepo.count({customerId});
    const isFirstOrder = existingOrders.count === 0;

    if (!isFirstOrder) {
      return {
        isEligible: false,
        appliedDiscount: null,
      };
    }

    // 2. Get active discount with conditions
    const now = new Date().toISOString();
    const activeDiscounts = await this.discountRepo.find({
      where: {
        isActive: true,
        startDate: {lte: now},
        endDate: {gte: now},
      },
      include: [{relation: 'discountConditions'}],
    });

    // Optional: Only one first-order discount allowed at a time
    const discount = activeDiscounts[0];
    if (!discount) {
      return {
        isEligible: false,
        appliedDiscount: null,
      };
    }

    const conditions = (discount.discountConditions ?? []).filter(c =>
      isFromApp ? true : !c.isAppOnly,
    );

    // Sort conditions by threshold ascending
    conditions.sort((a, b) => a.thresholdAmount - b.thresholdAmount);

    let bestApplicable: DiscountCondition | undefined;
    let nextTier: DiscountCondition | undefined;

    for (const condition of conditions) {
      if (cartTotal >= condition.thresholdAmount) {
        bestApplicable = condition;
      } else {
        nextTier = nextTier ?? condition;
        break;
      }
    }

    if (!bestApplicable) {
      const message = nextTier
        ? `Add ₹${nextTier.thresholdAmount - cartTotal} more to save ₹${nextTier.discountValue}`
        : undefined;

      return {
        isEligible: true,
        appliedDiscount: null,
        nextTierMessage: message,
      };
    }

    const discountAmount =
      bestApplicable.discountType === 'FLAT'
        ? bestApplicable.discountValue
        : (cartTotal * bestApplicable.discountValue) / 100;

    return {
      isEligible: true,
      appliedDiscount: {
        conditionId: bestApplicable.id,
        discountValue: Math.round(discountAmount),
        discountType: bestApplicable.discountType,
      },
      nextTierMessage: nextTier
        ? `Spend ₹${nextTier.thresholdAmount - cartTotal} more to get additional ₹${nextTier.discountValue} off`
        : undefined,
      discountBreakdownMessage: `You saved ₹${Math.round(discountAmount)} with your first-order offer!`,
    };
  }
}
