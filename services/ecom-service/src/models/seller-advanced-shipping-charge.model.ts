import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {SellerShippingProfile} from './seller-shipping-profile.model';

@model({name: 'seller_advanced_shipping_charges'})
export class SellerAdvancedShippingCharge extends UserModifiableEntity {
  @property({type: 'string', id: true, generated: true})
  id?: string;

  @property({type: 'string', required: true})
  state: string;

  @property({type: 'number', required: true, name: 'min_weight_grams'})
  minWeightGrams: number;

  @property({type: 'number', required: true, name: 'max_weight_grams'})
  maxWeightGrams: number;

  @property({type: 'number', required: true})
  price: number;

  @property({type: 'boolean', default: true, name: 'is_active'})
  isActive?: boolean;

  @belongsTo(
    () => SellerShippingProfile,
    {keyTo: 'id'},
    {name: 'shipping_profile_id'},
  )
  shippingProfileId: string;

  constructor(data?: Partial<SellerAdvancedShippingCharge>) {
    super(data);
  }
}

export interface SellerAdvancedShippingChargeRelations {
  shippingProfile?: SellerShippingProfile;
}

export type SellerAdvancedShippingChargeWithRelations =
  SellerAdvancedShippingCharge & SellerAdvancedShippingChargeRelations;
