import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {SellerShippingProfile} from './seller-shipping-profile.model';
import {ProductVariant} from './product-variant.model';

@model({name: 'seller_product_variant_shipping_charges'})
export class SellerProductVariantShippingCharge extends UserModifiableEntity {
  @property({type: 'string', id: true, generated: true})
  id?: string;

  @belongsTo(() => ProductVariant, {keyTo: 'id'}, {name: 'product_variant_id'})
  productVariantId: string;

  @property({type: 'string', required: true})
  state: string;

  @property({type: 'number', required: true})
  price: number;

  @property({type: 'boolean', default: true, name: 'is_active'})
  isActive?: boolean;

  @belongsTo(
    () => SellerShippingProfile,
    {keyTo: 'id'},
    {name: 'shipping_profile_id'},
  )
  shippingProfileId: string;

  constructor(data?: Partial<SellerProductVariantShippingCharge>) {
    super(data);
  }
}

export interface SellerProductVariantShippingChargeRelations {
  shippingProfile?: SellerShippingProfile;
}

export type SellerProductVariantShippingChargeWithRelations =
  SellerProductVariantShippingCharge &
    SellerProductVariantShippingChargeRelations;
