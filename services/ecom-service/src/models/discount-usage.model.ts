import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Discount} from './discount.model';
import {Order} from './order.model';

@model({name: 'discount_usages'})
export class DiscountUsage extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    name: 'customer_id',
  })
  customerId: string;

  @property({
    type: 'date',
    name: 'used_on',
  })
  usedOn?: string;

  @belongsTo(() => Discount, {keyTo: 'id'}, {name: 'discount_id'})
  discountId: string;

  @belongsTo(() => Order, {keyTo: 'id'}, {name: 'order_id'})
  orderId: string;

  constructor(data?: Partial<DiscountUsage>) {
    super(data);
  }
}

export interface DiscountUsageRelations {
  // describe navigational properties here
}

export type DiscountUsageWithRelations = DiscountUsage & DiscountUsageRelations;
