import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {
  ProductVariant,
  ProductVariantWithRelations,
} from './product-variant.model';

@model({settings: {strict: false}, name: 'wishlists'})
export class Wishlist extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'customer_id',
  })
  customerId: string;

  @belongsTo(
    () => ProductVariant,
    {keyTo: 'id'},
    {name: 'product_variant_id', required: true},
  )
  productVariantId: string;

  constructor(data?: Partial<Wishlist>) {
    super(data);
  }
}

export interface WishlistRelations {
  productVariant?: ProductVariantWithRelations;
}

export type WishlistWithRelations = Wishlist & WishlistRelations;
