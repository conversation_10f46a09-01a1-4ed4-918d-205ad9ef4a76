import {Model, model, property} from '@loopback/repository';
import {SellerBasicShippingCharge} from '../seller-basic-shipping-charge.model';
import {SellerAdvancedShippingCharge} from '../seller-advanced-shipping-charge.model';
import {SellerProductVariantShippingCharge} from '../seller-product-variant-shipping-charge.model';

@model()
export class SellerShippingProfileDto extends Model {
  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  isDefault?: boolean;

  @property({
    type: 'boolean',
    default: true,
  })
  isActive?: boolean;

  @property({
    type: 'string',
    required: true,
  })
  sellerId: string;

  @property({
    type: 'string',
    required: true,
  })
  shippingMethodId: string;

  @property({
    type: 'number',
    required: true,
  })
  fallbackMinDeliveryDays: number;

  @property({
    type: 'number',
    required: true,
  })
  fallbackMaxDeliveryDays: number;

  @property({
    type: 'number',
    required: true,
  })
  fallbackPrice: number;

  @property({
    type: 'array',
    itemType: 'object',
  })
  basicShippingCharges?: Partial<SellerBasicShippingCharge>[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  advancedShippingCharges?: Partial<SellerAdvancedShippingCharge>[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  productVariantShippingCharges?: Partial<SellerProductVariantShippingCharge>[];

  constructor(data?: Partial<SellerShippingProfileDto>) {
    super(data);
  }
}
