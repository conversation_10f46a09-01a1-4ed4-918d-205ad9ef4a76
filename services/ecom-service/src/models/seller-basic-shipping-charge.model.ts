import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {SellerShippingProfile} from './seller-shipping-profile.model';
import {BasicShippingStateType} from '@local/core';

@model({name: 'seller_basic_shipping_charges'})
export class SellerBasicShippingCharge extends UserModifiableEntity {
  @property({type: 'string', id: true, generated: true})
  id?: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(BasicShippingStateType),
    },
    name: 'state_type',
  })
  stateType: BasicShippingStateType;

  @property({type: 'number', required: true})
  price: number;

  @property({type: 'boolean', default: true, name: 'is_active'})
  isActive?: boolean;

  @belongsTo(
    () => SellerShippingProfile,
    {keyTo: 'id'},
    {name: 'shipping_profile_id'},
  )
  shippingProfileId: string;

  constructor(data?: Partial<SellerBasicShippingCharge>) {
    super(data);
  }
}

export interface SellerBasicShippingChargeRelations {
  shippingProfile?: SellerShippingProfile;
}

export type SellerBasicShippingChargeWithRelations = SellerBasicShippingCharge &
  SellerBasicShippingChargeRelations;
