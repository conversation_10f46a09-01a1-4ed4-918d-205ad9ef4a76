import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {STATUS_CODE, CONTENT_TYPE} from '@sourceloop/core';

import {SellerAdvancedShippingCharge} from '../models';
import {SellerAdvancedShippingChargeRepository} from '../repositories';

const basePath = '/advanced-shipping-charges';

export class SellerAdvancedShippingChargeController {
  constructor(
    @repository(SellerAdvancedShippingChargeRepository)
    public shippingChargeRepo: SellerAdvancedShippingChargeRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'SellerAdvancedShippingCharge model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerAdvancedShippingCharge),
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerAdvancedShippingCharge, {
            title: 'NewSellerAdvancedShippingCharge',
            exclude: ['id'],
          }),
        },
      },
    })
    shippingCharge: Omit<SellerAdvancedShippingCharge, 'id'>,
  ): Promise<SellerAdvancedShippingCharge> {
    return this.shippingChargeRepo.create(shippingCharge);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'SellerAdvancedShippingCharge count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(SellerAdvancedShippingCharge)
    where?: Where<SellerAdvancedShippingCharge>,
  ): Promise<Count> {
    return this.shippingChargeRepo.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of SellerAdvancedShippingCharge instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SellerAdvancedShippingCharge, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(SellerAdvancedShippingCharge)
    filter?: Filter<SellerAdvancedShippingCharge>,
  ): Promise<SellerAdvancedShippingCharge[]> {
    return this.shippingChargeRepo.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'SellerAdvancedShippingCharge instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerAdvancedShippingCharge, {
          includeRelations: true,
        }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(SellerAdvancedShippingCharge, {exclude: 'where'})
    filter?: FilterExcludingWhere<SellerAdvancedShippingCharge>,
  ): Promise<SellerAdvancedShippingCharge> {
    return this.shippingChargeRepo.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerAdvancedShippingCharge PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerAdvancedShippingCharge, {
            partial: true,
          }),
        },
      },
    })
    shippingCharge: Partial<SellerAdvancedShippingCharge>,
  ): Promise<void> {
    await this.shippingChargeRepo.updateById(id, shippingCharge);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerAdvancedShippingCharge DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.shippingChargeRepo.deleteById(id);
  }
}
