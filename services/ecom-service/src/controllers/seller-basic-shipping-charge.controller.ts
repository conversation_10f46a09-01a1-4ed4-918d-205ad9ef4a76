import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {STATUS_CODE, CONTENT_TYPE} from '@sourceloop/core';
import {SellerBasicShippingCharge} from '../models';
import {SellerBasicShippingChargeRepository} from '../repositories';

const basePath = '/basic-shipping-charges';

export class SellerBasicShippingChargeController {
  constructor(
    @repository(SellerBasicShippingChargeRepository)
    public sellerBasicShippingChargeRepository: SellerBasicShippingChargeRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'SellerBasicShippingCharge model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerBasicShippingCharge),
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerBasicShippingCharge, {
            title: 'NewSellerBasicShippingCharge',
            exclude: ['id'],
          }),
        },
      },
    })
    sellerBasicShippingCharge: Omit<SellerBasicShippingCharge, 'id'>,
  ): Promise<SellerBasicShippingCharge> {
    return this.sellerBasicShippingChargeRepository.create(
      sellerBasicShippingCharge,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'SellerBasicShippingCharge model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(SellerBasicShippingCharge)
    where?: Where<SellerBasicShippingCharge>,
  ): Promise<Count> {
    return this.sellerBasicShippingChargeRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of SellerBasicShippingCharge model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SellerBasicShippingCharge, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(SellerBasicShippingCharge)
    filter?: Filter<SellerBasicShippingCharge>,
  ): Promise<SellerBasicShippingCharge[]> {
    return this.sellerBasicShippingChargeRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerBasicShippingCharge PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerBasicShippingCharge, {partial: true}),
        },
      },
    })
    sellerBasicShippingCharge: Partial<SellerBasicShippingCharge>,
  ): Promise<void> {
    await this.sellerBasicShippingChargeRepository.updateById(
      id,
      sellerBasicShippingCharge,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerBasicShippingCharge DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.sellerBasicShippingChargeRepository.deleteById(id);
  }
}
