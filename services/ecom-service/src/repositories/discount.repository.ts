import {Getter, inject} from '@loopback/core';
import {PgDataSource} from '../datasources';
import {Discount, DiscountRelations, DiscountCondition} from '../models';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {repository, HasManyRepositoryFactory} from '@loopback/repository';
import {DiscountConditionRepository} from './discount-condition.repository';

export class DiscountRepository extends SequelizeUserModifyCrudRepositoryCore<
  Discount,
  typeof Discount.prototype.id,
  DiscountRelations
> {

  public readonly discountConditions: HasManyRepositoryFactory<DiscountCondition, typeof Discount.prototype.id>;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>, @repository.getter('DiscountConditionRepository') protected discountConditionRepositoryGetter: Getter<DiscountConditionRepository>,
  ) {
    super(Discount, dataSource, getCurrentUser);
    this.discountConditions = this.createHasManyRepositoryFactoryFor('discountConditions', discountConditionRepositoryGetter,);
    this.registerInclusionResolver('discountConditions', this.discountConditions.inclusionResolver);
  }
}
