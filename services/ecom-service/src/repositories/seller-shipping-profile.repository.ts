import {Getter, inject} from '@loopback/core';
import {
  BelongsToAccessor,
  HasManyRepositoryFactory,
  repository,
} from '@loopback/repository';
import {PgDataSource} from '../datasources';
import {
  SellerShippingProfile,
  SellerShippingProfileRelations,
  ShippingMethod,
  SellerBasicShippingCharge,
  SellerAdvancedShippingCharge,
  SellerProductVariantShippingCharge,
} from '../models';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {ShippingMethodRepository} from './shipping-method.repository';
import {SellerBasicShippingChargeRepository} from './seller-basic-shipping-charge.repository';
import {SellerAdvancedShippingChargeRepository} from './seller-advanced-shipping-charge.repository';
import {SellerProductVariantShippingChargeRepository} from './seller-product-variant-shipping-charge.repository';

export class SellerShippingProfileRepository extends SequelizeUserModifyCrudRepositoryCore<
  SellerShippingProfile,
  typeof SellerShippingProfile.prototype.id,
  SellerShippingProfileRelations
> {
  public readonly shippingMethod: BelongsToAccessor<
    ShippingMethod,
    typeof SellerShippingProfile.prototype.id
  >;

  public readonly basicShippingCharges: HasManyRepositoryFactory<
    SellerBasicShippingCharge,
    typeof SellerShippingProfile.prototype.id
  >;

  public readonly advancedShippingCharges: HasManyRepositoryFactory<
    SellerAdvancedShippingCharge,
    typeof SellerShippingProfile.prototype.id
  >;

  public readonly productVariantShippingCharges: HasManyRepositoryFactory<
    SellerProductVariantShippingCharge,
    typeof SellerShippingProfile.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('ShippingMethodRepository')
    protected shippingMethodRepositoryGetter: Getter<ShippingMethodRepository>,
    @repository.getter('SellerBasicShippingChargeRepository')
    protected sellerBasicShippingChargeRepositoryGetter: Getter<SellerBasicShippingChargeRepository>,
    @repository.getter('SellerAdvancedShippingChargeRepository')
    protected sellerAdvancedShippingChargeRepositoryGetter: Getter<SellerAdvancedShippingChargeRepository>,
    @repository.getter('SellerProductVariantShippingChargeRepository')
    protected sellerProductVariantShippingChargeRepositoryGetter: Getter<SellerProductVariantShippingChargeRepository>,
  ) {
    super(SellerShippingProfile, dataSource, getCurrentUser);

    this.shippingMethod = this.createBelongsToAccessorFor(
      'shippingMethod',
      shippingMethodRepositoryGetter,
    );
    this.registerInclusionResolver(
      'shippingMethod',
      this.shippingMethod.inclusionResolver,
    );

    this.basicShippingCharges = this.createHasManyRepositoryFactoryFor(
      'basicShippingCharges',
      sellerBasicShippingChargeRepositoryGetter,
    );
    this.registerInclusionResolver(
      'basicShippingCharges',
      this.basicShippingCharges.inclusionResolver,
    );

    this.advancedShippingCharges = this.createHasManyRepositoryFactoryFor(
      'advancedShippingCharges',
      sellerAdvancedShippingChargeRepositoryGetter,
    );
    this.registerInclusionResolver(
      'advancedShippingCharges',
      this.advancedShippingCharges.inclusionResolver,
    );

    this.productVariantShippingCharges = this.createHasManyRepositoryFactoryFor(
      'productVariantShippingCharges',
      sellerProductVariantShippingChargeRepositoryGetter,
    );
    this.registerInclusionResolver(
      'productVariantShippingCharges',
      this.productVariantShippingCharges.inclusionResolver,
    );
  }
}
