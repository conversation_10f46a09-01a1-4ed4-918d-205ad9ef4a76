import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, repository} from '@loopback/repository';
import {PgDataSource} from '../datasources';
import {
  SellerBasicShippingCharge,
  SellerBasicShippingChargeRelations,
  SellerShippingProfile,
} from '../models';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {SellerShippingProfileRepository} from './seller-shipping-profile.repository';

export class SellerBasicShippingChargeRepository extends SequelizeUserModifyCrudRepositoryCore<
  SellerBasicShippingCharge,
  typeof SellerBasicShippingCharge.prototype.id,
  SellerBasicShippingChargeRelations
> {
  public readonly shippingProfile: BelongsToAccessor<
    SellerShippingProfile,
    typeof SellerBasicShippingCharge.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('SellerShippingProfileRepository')
    protected sellerShippingProfileRepositoryGetter: Getter<SellerShippingProfileRepository>,
  ) {
    super(SellerBasicShippingCharge, dataSource, getCurrentUser);

    this.shippingProfile = this.createBelongsToAccessorFor(
      'shippingProfile',
      sellerShippingProfileRepositoryGetter,
    );
    this.registerInclusionResolver(
      'shippingProfile',
      this.shippingProfile.inclusionResolver,
    );
  }
}
