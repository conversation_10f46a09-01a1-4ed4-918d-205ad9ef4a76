import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, repository} from '@loopback/repository';
import {PgDataSource} from '../datasources';
import {
  SellerProductVariantShippingCharge,
  SellerProductVariantShippingChargeRelations,
  SellerShippingProfile,
  ProductVariant,
} from '../models';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {SellerShippingProfileRepository} from './seller-shipping-profile.repository';
import {ProductVariantRepository} from './product-variant.repository';

export class SellerProductVariantShippingChargeRepository extends SequelizeUserModifyCrudRepositoryCore<
  SellerProductVariantShippingCharge,
  typeof SellerProductVariantShippingCharge.prototype.id,
  SellerProductVariantShippingChargeRelations
> {
  public readonly shippingProfile: BelongsToAccessor<
    SellerShippingProfile,
    typeof SellerProductVariantShippingCharge.prototype.id
  >;

  public readonly productVariant: BelongsToAccessor<
    ProductVariant,
    typeof SellerProductVariantShippingCharge.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('SellerShippingProfileRepository')
    protected sellerShippingProfileRepositoryGetter: Getter<SellerShippingProfileRepository>,
    @repository.getter('ProductVariantRepository')
    protected productVariantRepositoryGetter: Getter<ProductVariantRepository>,
  ) {
    super(SellerProductVariantShippingCharge, dataSource, getCurrentUser);

    this.shippingProfile = this.createBelongsToAccessorFor(
      'shippingProfile',
      sellerShippingProfileRepositoryGetter,
    );
    this.registerInclusionResolver(
      'shippingProfile',
      this.shippingProfile.inclusionResolver,
    );

    this.productVariant = this.createBelongsToAccessorFor(
      'productVariant',
      productVariantRepositoryGetter,
    );
    this.registerInclusionResolver(
      'productVariant',
      this.productVariant.inclusionResolver,
    );
  }
}
