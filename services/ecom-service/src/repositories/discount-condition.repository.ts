import {Getter, inject} from '@loopback/core';
import {PgDataSource} from '../datasources';
import {DiscountCondition, DiscountConditionRelations, Discount} from '../models';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {repository, BelongsToAccessor} from '@loopback/repository';
import {DiscountRepository} from './discount.repository';

export class DiscountConditionRepository extends SequelizeUserModifyCrudRepositoryCore<
  DiscountCondition,
  typeof DiscountCondition.prototype.id,
  DiscountConditionRelations
> {

  public readonly discount: BelongsToAccessor<Discount, typeof DiscountCondition.prototype.id>;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>, @repository.getter('DiscountRepository') protected discountRepositoryGetter: Getter<DiscountRepository>,
  ) {
    super(DiscountCondition, dataSource, getCurrentUser);
    this.discount = this.createBelongsToAccessorFor('discount', discountRepositoryGetter,);
    this.registerInclusionResolver('discount', this.discount.inclusionResolver);
  }
}
