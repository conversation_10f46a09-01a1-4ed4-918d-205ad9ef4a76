/* eslint-disable @typescript-eslint/naming-convention */
import {injectable, BindingScope, service, inject} from '@loopback/core';
import {HttpErrors} from '@loopback/rest';
import axios from 'axios';
import {ZohoTokenService} from './zoho-token.service';
import {FCMBindings} from '../keys';
import {FCMConfig} from '../types';
import * as admin from 'firebase-admin';

type FCMAppsMap = Record<string, admin.messaging.Messaging>;
@injectable({scope: BindingScope.TRANSIENT})
export class TopicService {
  private fcmClients: FCMAppsMap = {};
  constructor(
    @service(ZohoTokenService)
    private readonly zohoTokenService: ZohoTokenService,
    @inject(FCMBindings.Config, {optional: false})
    private readonly fcmConfigs: FCMConfig[],
  ) {
    if (!fcmConfigs || fcmConfigs.length === 0) {
      throw new HttpErrors.PreconditionFailed('FCM configs are missing!');
    }

    for (const config of fcmConfigs) {
      const appName = config.projectId;

      try {
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        admin.app(appName).delete();
      } catch (e) {
        // App might not exist yet – ignore
      }

      const app = admin.initializeApp(
        {
          credential: admin.credential.cert({
            projectId: config.projectId,
            clientEmail: config.clientEmail,
            privateKey: config.privateKey.replace(/\\n/g, '\n'),
          }),
        },
        appName,
      );

      this.fcmClients[appName] = app.messaging();
    }
  }

  async createZohoTopic(name: string): Promise<string> {
    const accessToken = await this.zohoTokenService.getValidAccessToken();

    const details = {
      topic_name: name,
      topic_desc: name,
    };

    try {
      const response = await axios.post(
        `https://campaigns.zoho.in/api/v1.1/topics?details=${encodeURIComponent(JSON.stringify(details))}`,
        null,
        {
          headers: {
            Authorization: `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      return response.data?.topic_id;
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        'Zoho Topic Creation failed: ' + (err.response?.data || err.message),
      );
    }
  }

  async subscribeToFcmTopic(topic: string, fcmToken: string) {
    const [config] = this.fcmConfigs;
    const fcmClient = this.fcmClients[config.projectId];
    if (!fcmClient) {
      throw new HttpErrors.InternalServerError(
        `No FCM client found for projectId: ${config.projectId}`,
      );
    }

    try {
      await fcmClient.subscribeToTopic([fcmToken], topic);
      console.log(`✅ Subscribed to FCM topic: ${topic}`);
    } catch (err) {
      throw new HttpErrors.InternalServerError(
        `❌ FCM Subscription failed: ${err.message || err}`,
      );
    }
  }
}
