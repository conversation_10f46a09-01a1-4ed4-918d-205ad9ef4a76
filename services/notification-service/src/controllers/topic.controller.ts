import {service} from '@loopback/core';
import {TopicService} from '../services';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {post, requestBody, response} from '@loopback/rest';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';

const basePath = '/topics';

export class TopicController {
  constructor(
    @service(TopicService)
    private readonly topicService: TopicService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCampaign]})
  @post(`${basePath}/subscribe-fcm`)
  @response(STATUS_CODE.OK, {
    description: 'Subscribe FCM token to topic',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'object',
          properties: {
            success: {type: 'boolean'},
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async subscribeToFcmTopic(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
            required: ['topic', 'fcmToken'],
            properties: {
              topic: {type: 'string'},
              fcmToken: {type: 'string'},
            },
          },
        },
      },
    })
    payload: {
      topic: string;
      fcmToken: string;
    },
  ): Promise<{success: boolean; message: string}> {
    const {topic, fcmToken} = payload;
    await this.topicService.subscribeToFcmTopic(topic, fcmToken);
    return {
      success: true,
      message: `Successfully subscribed to topic: ${topic}`,
    };
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCampaign]})
  @post(`${basePath}/create-zoho`)
  @response(STATUS_CODE.OK, {
    description: 'Create Zoho topic',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'object',
          properties: {
            topicId: {type: 'string'},
          },
        },
      },
    },
  })
  async createZohoTopic(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
            required: ['name'],
            properties: {
              name: {type: 'string'},
            },
          },
        },
      },
    })
    payload: {
      name: string;
    },
  ): Promise<{topicId: string}> {
    const {name} = payload;
    const topicId = await this.topicService.createZohoTopic(name);
    return {topicId};
  }
}
