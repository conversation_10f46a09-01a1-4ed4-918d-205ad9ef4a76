import {useEffect, useState} from 'react';
import {
  Image,
  KeyboardAvoidingView,
  Modal,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {Asset, launchImageLibrary} from 'react-native-image-picker';
import {Button, HelperText, Icon, Text} from 'react-native-paper';
import {colors} from '../../../../theme/colors';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import CustomTextInput from '../../../../components/InputFields/CustomTextInput';
import customColors from '../../../../theme/customColors';
import {useCreateSupportTicketMutation} from '../../../../redux/help/helpApiSlice';
import Toast from 'react-native-toast-message';
import {SupportStatus, TicketCategory} from '../../../../enums/help.enum';
import {CreateTicketDto} from '../../../../types/help';

type Props = {visible: boolean; onClose: () => void};

const SupportTicketModal: React.FC<Props> = ({visible, onClose}) => {
  const [images, setImages] = useState<Asset[]>([]);
  const [createTicket, {isLoading}] = useCreateSupportTicketMutation();

  const pickImages = async () => {
    const res = await launchImageLibrary({
      mediaType: 'photo',
      selectionLimit: 5,
    });
    if (!res.didCancel && res.assets) {
      setImages(prev => [...prev, ...(res.assets ?? [])]);
    }
  };

  const validationSchema = Yup.object({
    title: Yup.string().required('Title is required'),
    summary: Yup.string().required('Summary is required'),
    category: Yup.string().required('Category is required'),
  });

  const formik = useFormik({
    initialValues: {title: '', summary: '', category: ''},
    validationSchema,
    enableReinitialize: true,
    onSubmit: async values => {
      const fd = new FormData();
      fd.append('title', values.title);

      fd.append('summary', values.summary);

      fd.append('category', values.category);

      images.forEach((img, idx) => {
        const file = {
          uri: img.uri!,
          name:
            img.fileName ??
            `screenshot-${idx}.${img.uri?.split('.').pop() ?? 'jpg'}`,
          type: img.type ?? 'image/jpeg',
        };
        fd.append('attachments', file as any);
      });
      const body: CreateTicketDto = {
        title: values.title,
        description: values.summary,
        status: SupportStatus.Open,
        category: values.category as TicketCategory,
      };

      await createTicket(body).unwrap;
      Toast.show({type: 'success', text1: 'Ticket created!'});
      formik.resetForm();
      setImages([]);
      onClose();
    },
  });
  useEffect(() => {
    if (!visible) {
      formik.resetForm();
      setImages([]);
      setMenuOpen(false);
    }
  }, [formik, visible]);
  const categoryOptions = [
    {label: 'Orders', value: TicketCategory.Orders},
    {label: 'Payments', value: TicketCategory.Payments},
    {label: 'Refunds', value: TicketCategory.Refunds},
    {label: 'Shipping', value: TicketCategory.Shipping},
    {label: 'Returns', value: TicketCategory.Returns},
    {label: 'Product Inquiry', value: TicketCategory.ProductInquiry},
    {label: 'Technical Issue', value: TicketCategory.TechnicalIssue},
    {label: 'Account', value: TicketCategory.Account},
    {label: 'Other', value: TicketCategory.Other},
  ];
  const [menuOpen, setMenuOpen] = useState(false);

  return (
    <Modal visible={visible} animationType="slide" transparent>
      <SafeAreaView style={styles.modalBackdrop}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={styles.modalContainer}>
          <ScrollView style={styles.formContainer}>
            <Text style={styles.modalTitle}>Create Support Ticket</Text>

            <CustomTextInput
              title="Title"
              value={formik.values.title}
              onChangeText={formik.handleChange('title')}
              onBlur={formik.handleBlur('title')}
              touched={formik.touched.title}
              errors={formik.errors.title}
              isHelperTextVisible={
                !!formik.touched.title && !!formik.errors.title
              }
            />

            <CustomTextInput
              title="Summary"
              numberOfLines={4}
              value={formik.values.summary}
              onChangeText={formik.handleChange('summary')}
              onBlur={formik.handleBlur('summary')}
              touched={formik.touched.summary}
              errors={formik.errors.summary}
              isHelperTextVisible={
                !!formik.touched.summary && !!formik.errors.summary
              }
            />

            <View style={{marginBottom: 16}}>
              <Text style={styles.dropdownLabel}>Category</Text>
              <TouchableOpacity
                style={styles.dropdownInput}
                onPress={() => setMenuOpen(true)}>
                <Text style={{color: formik.values.category ? '#000' : '#999'}}>
                  {categoryOptions.find(
                    opt => opt.value === formik.values.category,
                  )?.label || 'Select a category'}{' '}
                </Text>
                <Icon source="chevron-down" size={20} color="#666" />
              </TouchableOpacity>

              <Modal
                visible={menuOpen}
                transparent
                animationType="fade"
                onRequestClose={() => setMenuOpen(false)}>
                <TouchableOpacity
                  style={styles.modalBackdrop}
                  activeOpacity={1}
                  onPressOut={() => setMenuOpen(false)}>
                  <View style={styles.modalOptionsContainer}>
                    {categoryOptions.map(option => (
                      <TouchableOpacity
                        key={option.value}
                        onPress={() => {
                          formik.setFieldValue('category', option.value);
                          setMenuOpen(false);
                        }}
                        style={styles.optionItem}>
                        <Text style={styles.optionText}>{option.label}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </TouchableOpacity>
              </Modal>

              {formik.touched.category && formik.errors.category && (
                <HelperText type="error">{formik.errors.category}</HelperText>
              )}
            </View>

            <TouchableOpacity style={styles.imageButton} onPress={pickImages}>
              <Text style={styles.imageButtonText}>Upload Screenshot</Text>
            </TouchableOpacity>

            <View style={styles.imagePreviewContainer}>
              {images.map((img, i) => (
                <View key={i} style={styles.imageWrapper}>
                  <Image source={{uri: img.uri}} style={styles.imagePreview} />
                  <TouchableOpacity
                    style={styles.removeIcon}
                    onPress={() =>
                      setImages(prev => prev.filter((_, idx) => idx !== i))
                    }>
                    <Text style={styles.removeText}>✕</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>

            <View style={styles.buttonRow}>
              <Button
                mode="outlined"
                style={styles.modalButton}
                onPress={() => {
                  formik.resetForm();
                  setImages([]);
                  onClose();
                }}>
                Cancel
              </Button>
              <Button
                mode="contained"
                loading={isLoading}
                disabled={isLoading}
                style={styles.modalButton}
                onPress={formik.handleSubmit}>
                Submit
              </Button>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>
  );
};

export default SupportTicketModal;
const styles = StyleSheet.create({
  supportButton: {
    marginVertical: 20,
    marginHorizontal: 16,
    backgroundColor: colors.tertiary,
  },
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: customColors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    maxHeight: '90%',
  },
  formContainer: {
    flexGrow: 1,
    padding: 10,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.gray.medium,
    borderRadius: 8,
    padding: 10,
    marginBottom: 12,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    marginBottom: 8,
  },
  imageButton: {
    backgroundColor: colors.gray.light,
    padding: 10,
    borderRadius: 18,
    alignItems: 'center',
    marginBottom: 12,
    marginTop: 20,
  },
  imageButtonText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
    marginBottom: 10,
  },
  modalButton: {
    flex: 1,
  },
  imagePreviewContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginBottom: 12,
  },
  imageWrapper: {
    position: 'relative',
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  removeIcon: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: colors.error,
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeText: {
    color: customColors.white,
    fontSize: 14,
    fontWeight: 'bold',
  },
  dropdownLabel: {
    fontSize: 14,
    color: customColors.textBlack,
    marginBottom: 4,
    marginLeft: 16,
  },
  dropdownInput: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 12,
    borderRadius: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalOptionsContainer: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginTop: 100,
    borderRadius: 8,
    padding: 16,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    marginBottom: 40,
    marginLeft: 20,
    marginRight: 20,
  },
  optionItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
});
