import React from 'react';
import {View, Text, StyleSheet, Image} from 'react-native';
import {IconButton} from 'react-native-paper';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';
type ChatHeaderProps = {
  onBackPress: () => void;
  user: {
    avatar: string;
    fname: string;
    lname: string;
    online: boolean;
  };
};

export const ChatHeader = ({onBackPress, user}: ChatHeaderProps) => {
  const initials = (() => {
    const f = user.fname?.trim()?.toUpperCase() || '';
    const l = user.lname?.trim()?.toUpperCase() || '';
    const result = (f + l).slice(0, 2);
    return result || 'U';
  })();

  return (
    <View style={styles.header}>
      <IconButton
        icon="chevron-left"
        size={38}
        iconColor={colors.tertiary}
        onPress={onBackPress}
        style={styles.backButton}
      />

      <View style={styles.profileContainer}>
        {user.avatar ? (
          <Image source={{uri: user.avatar}} style={styles.avatar} />
        ) : (
          <View style={styles.initialsCircle}>
            <Text style={styles.initialsText}>{initials}</Text>
          </View>
        )}

        <View style={styles.userInfo}>
          <Text style={styles.name}>
            {[user.fname, user.lname].filter(Boolean).join(' ') || 'Unknown'}
          </Text>
          <Text style={styles.status}>
            {user.online ? 'Online' : 'Last seen recently'}
          </Text>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  header: {
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: customColors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray.backGround,
    elevation: 2,
    shadowColor: customColors.textBlack,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  userInfo: {
    marginLeft: 10,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: customColors.textBlack,
  },
  status: {
    fontSize: 12,
    color: colors.gray.dark,
  },
  backButton: {
    margin: 0,
    padding: 0,
  },
  initialsCircle: {
    width: 35,
    height: 35,
    borderRadius: 22,
    backgroundColor: colors.tertiary,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: customColors.white,
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
