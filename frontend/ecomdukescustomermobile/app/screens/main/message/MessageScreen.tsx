import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {KeyboardAvoidingView, Platform, StyleSheet, View} from 'react-native';
import {
  GiftedChat,
  IMessage,
  Bubble,
  BubbleProps,
  InputToolbar,
  InputToolbarProps,
  Send,
  SendProps,
} from 'react-native-gifted-chat';
import {Icon} from 'react-native-paper';
import {SafeAreaView} from 'react-native-safe-area-context';
import {RouteProp, useRoute} from '@react-navigation/native';

import {
  useGetUserChatsQuery,
  useCreateChatMutation,
  useGetChatMessagesQuery,
  useSendChatMessageMutation,
} from '../../../redux/chat/chatApiSlice';
import {ChatDto, ChatMessage} from '../../../types/chat';
import {useTypedSelector} from '../../../redux/store';
import {
  MessageNavigationProp,
  OnboardStackParamList,
} from '../../../navigations/types';
import FullScreenLoader from '../../../components/FullScreenLoader';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import {ChatHeader} from './components/ChatHeader';

interface ScreenProps {
  navigation: MessageNavigationProp;
  chat: ChatDto | null;
}

const MessageScreen: React.FC<ScreenProps> = ({navigation, chat}) => {
  const {sellerId, user: passedUser} =
    useRoute<RouteProp<OnboardStackParamList, 'message'>>().params;

  const [chatId, setChatId] = useState<string | null>(chat?.id ?? null);

  const {
    data: userChats = [],
    isLoading: userChatLoading,
    refetch: refetchChats,
  } = useGetUserChatsQuery({search: ''});

  const [createChat, {isLoading: createChatLoading}] = useCreateChatMutation();

  useEffect(() => {
    if (!sellerId || !userChats.length) return;

    const existing = userChats.find(c => c.sellerId === sellerId);

    if (existing?.id && existing.id !== chatId) {
      setChatId(existing.id);
    } else if (!existing && !chatId) {
      createChat({sellerId})
        .unwrap()
        .then(newChat => {
          setChatId(newChat.id);
          refetchChats();
        })
        .catch(err => console.error('Failed to create chat', err));
    }
  }, [sellerId, userChats, chatId, createChat, refetchChats]);

  const {data: messageDtos = [], isLoading: chatMessageLoading} =
    useGetChatMessagesQuery(chatId ?? '', {
      skip: !chatId,
      pollingInterval: 5000,
    });

  const [sendChatMessage] = useSendChatMessageMutation();

  const customerId = useTypedSelector(s => s.auth.userDetails?.id) ?? 'me';

  const serverMessages = useMemo<IMessage[]>(() => {
    return messageDtos
      .map(dto => mapToGifted(dto, customerId))
      .sort((a, b) => a.createdAt.valueOf() - b.createdAt.valueOf());
  }, [messageDtos, customerId]);

  const [localMsgs, setLocalMsgs] = useState<IMessage[]>([]);
  useEffect(() => setLocalMsgs([]), [chatId]);
  const mergedMsgs = useMemo<IMessage[]>(() => {
    const byId = new Map<string, IMessage>();
    [...serverMessages, ...localMsgs].forEach(m => byId.set(String(m._id), m));
    return [...byId.values()].sort(
      (a, b) => a.createdAt.valueOf() - b.createdAt.valueOf(),
    );
  }, [serverMessages, localMsgs]);

  const onSend = useCallback(
    async (msgs: IMessage[] = []) => {
      if (!chatId || !msgs.length) return;

      const optimistic: IMessage = {
        ...msgs[0],
        _id: `tmp-${Date.now()}`,
      };
      setLocalMsgs(prev => GiftedChat.append(prev, [optimistic]));

      const saved = await sendChatMessage({
        chatId,
        message: {message: optimistic.text},
      }).unwrap();

      const confirmed = mapToGifted(saved, customerId);
      setLocalMsgs(prev =>
        prev.filter(m => m._id !== optimistic._id).concat(confirmed),
      );
    },
    [chatId, sendChatMessage, customerId],
  );

  const loading = userChatLoading || createChatLoading || chatMessageLoading;

  const currentChat = useMemo(
    () => userChats.find(c => c.sellerId === sellerId),
    [userChats, sellerId],
  );

  // fallback from userChats if passedUser is not provided
  const fallbackUser = currentChat?.userData?.userTenant?.user;

  const fullNameArray = passedUser
    ? [passedUser.firstName, passedUser.lastName]
    : fallbackUser
    ? [fallbackUser.firstName, fallbackUser.lastName]
    : ['Unknown'];

  const [fname = '', lname = ''] = fullNameArray;

  const avatarUrl = passedUser?.photoUrl ?? fallbackUser?.photoUrl ?? '';

  return (
    <SafeAreaView style={styles.safeArea}>
      {loading && <FullScreenLoader />}

      <ChatHeader
        onBackPress={() => navigation.goBack()}
        user={{fname, lname, avatar: avatarUrl, online: false}}
      />

      <GiftedChat
        messages={mergedMsgs}
        onSend={onSend}
        user={{_id: customerId}}
        renderBubble={renderBubble}
        renderSend={renderSend}
        renderInputToolbar={renderInputToolbar}
        renderAvatar={null}
        placeholder="Type your message..."
        alwaysShowSend
        inverted={false}
        bottomOffset={Platform.OS === 'ios' ? 20 : 0}
      />

      {Platform.OS === 'ios' && (
        <KeyboardAvoidingView behavior="padding" keyboardVerticalOffset={80} />
      )}
    </SafeAreaView>
  );
};

export default MessageScreen;

const mapToGifted = (dto: ChatMessage, customerId: string): IMessage => ({
  _id: dto.id,
  text: dto.message,
  createdAt: new Date(dto.createdOn),
  user: {
    _id: dto.senderId === customerId ? customerId : dto.senderId,
    name: dto.sender?.firstName ?? '',
    avatar: dto.sender?.presignedPhotoUrl ?? '',
  },
});

const renderBubble = (
  props: React.JSX.IntrinsicAttributes & BubbleProps<IMessage>,
) => (
  <Bubble
    {...props}
    wrapperStyle={{
      left: {
        backgroundColor: colors.gray.backGround,
        marginBottom: 8,
      },
      right: {
        backgroundColor: colors.tertiary,
        marginBottom: 8,
      },
    }}
    textStyle={{
      right: {color: customColors.white},
      left: {color: customColors.textBlack},
    }}
  />
);

const renderSend = (
  props: React.JSX.IntrinsicAttributes & SendProps<IMessage>,
) => (
  <Send {...props}>
    <View style={styles.sendButton}>
      <Icon source="send" size={25} color={colors.tertiary} />
    </View>
  </Send>
);

const renderInputToolbar = (
  props: React.JSX.IntrinsicAttributes & InputToolbarProps<IMessage>,
) => <InputToolbar {...props} containerStyle={styles.inputToolbar} />;

const styles = StyleSheet.create({
  safeArea: {flex: 1, backgroundColor: customColors.white},
  sendButton: {marginRight: 20, marginBottom: 5},
  inputToolbar: {
    borderTopColor: colors.gray.dark,
    paddingVertical: 6,
    marginLeft: 20,
  },
});
