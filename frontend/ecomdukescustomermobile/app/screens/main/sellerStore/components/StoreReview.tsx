import React, {useMemo, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';

type ApiReview = {
  id: string;
  review: string;
  rating: number;
  createdOn: string;
  reviewAssets: string[];
};

interface ProductVariantWithReviews {
  id: string;
  name: string;
  reviews?: ApiReview[];
  featuredAsset?: {preview: string};
}

interface Review {
  id: string;
  text: string;
  author: string;
  date: string;
  rating: number;
  avatar: string;
  previewAssets: string[];
  reviewAssets: string[];
  productId: string;
  productName: string;
  productThumbnail?: string;
}

interface StoreReviewsProps {
  productVariants: ProductVariantWithReviews[];
  isLoading: boolean;
  isError: boolean;
}

const StoreReviews: React.FC<StoreReviewsProps> = ({
  productVariants,
  isLoading,
  isError,
}) => {
  const [showAllReviews, setShowAllReviews] = useState(false);

  const reviews = useMemo(() => {
    if (isLoading || isError || !productVariants) return [];

    const assetBaseUrl = 'https://assets.ecomdukes.in';

    const all: Review[] = productVariants.flatMap(variant =>
      (variant.reviews || []).map(apiReview => ({
        id: apiReview.id || `review-${Math.random().toString(36).slice(2, 9)}`,
        text: apiReview.review || 'No review text provided.',
        author: 'Customer',
        date: new Date(apiReview.createdOn).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        }),
        rating: apiReview.rating,
        avatar:
          'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_960_720.png',
        previewAssets: apiReview.reviewAssets.map(
          asset => `${assetBaseUrl}/${asset}`,
        ),
        reviewAssets: apiReview.reviewAssets.map(
          asset => `${assetBaseUrl}/${asset}`,
        ),
        productId: variant.id,
        productName: variant.name,
        productThumbnail: variant.featuredAsset?.preview
          ? `${assetBaseUrl}/${variant.featuredAsset.preview}`
          : undefined,
      })),
    );

    return all.sort((a, b) => b.rating - a.rating);
  }, [productVariants, isLoading, isError]);

  const visibleReviews = showAllReviews ? reviews : reviews.slice(0, 2);

  if (isLoading) {
    return (
      <View style={styles.stateWrapper}>
        <ActivityIndicator />
        <Text style={styles.stateText}>Loading reviews…</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.stateWrapper}>
        <Text style={[styles.stateText, {color: 'red'}]}>
          Error loading reviews.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Reviews</Text>

      <FlatList
        data={visibleReviews}
        keyExtractor={item => item.id}
        renderItem={({item}) => (
          <View style={styles.reviewCard}>
            <View style={styles.reviewHeader}>
              <Image source={{uri: item.avatar}} style={styles.avatar} />
              <View style={{flex: 1, marginLeft: 10}}>
                <Text style={styles.author}>{item.author}</Text>
                <Text style={styles.date}>{item.date}</Text>
              </View>
            </View>

            <Text style={styles.reviewText}>{item.text}</Text>

            {item.previewAssets?.length > 0 && (
              <View style={styles.assetRow}>
                {item.previewAssets.map((asset, index) => (
                  <Image
                    key={index}
                    source={{uri: asset}}
                    style={styles.reviewImage}
                    resizeMode="cover"
                  />
                ))}
              </View>
            )}
          </View>
        )}
        ItemSeparatorComponent={() => <View style={{height: 12}} />}
        scrollEnabled={false} // disable internal scroll if inside ScrollView
      />

      {!showAllReviews && reviews.length > 2 && (
        <View style={styles.buttonWrapper}>
          <TouchableOpacity
            style={styles.outlinedButton}
            onPress={() => setShowAllReviews(true)}>
            <Text style={styles.buttonText}>See more Reviews</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default StoreReviews;
const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingBottom: 24,
    backgroundColor: '#fff',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 16,
    color: '#000',
  },

  buttonWrapper: {
    alignItems: 'center',
    marginTop: 16,
  },
  outlinedButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderColor: '#000',
    borderRadius: 8,
  },
  buttonText: {
    color: '#000',
    fontWeight: '600',
  },
  stateWrapper: {
    paddingVertical: 32,
    alignItems: 'center',
  },
  stateText: {
    marginTop: 6,
    fontSize: 14,
    color: '#555',
  },
  reviewCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
    marginBottom: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  author: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  date: {
    fontSize: 12,
    color: '#666',
  },
  rating: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  reviewText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 6,
  },
  assetRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  reviewImage: {
    width: 60,
    height: 60,
    borderRadius: 6,
    marginRight: 6,
    marginTop: 6,
  },
});
