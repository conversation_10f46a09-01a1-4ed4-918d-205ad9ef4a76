import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {Icon} from 'react-native-paper';
import {SCREEN_NAME} from '../constants/screenNames';
import customColors from '../theme/customColors';
import HomeScreen from '../screens/main/home/<USER>';
import OrderConfirmation from '../screens/main/cart/OrderConfirmation';
import MyOrderScreen from '../screens/main/Orders/MyOrderScreen';
import WishlistScreen from '../screens/main/wishlist/WishlistScreen';
import SidebarMenu from '../screens/sidebar/SidebarMenu';
import {colors} from '../theme/colors';

const Tab = createBottomTabNavigator();

const BottomTabNavigation = () => {
  return (
    <Tab.Navigator
      initialRouteName={SCREEN_NAME.HOME}
      screenOptions={({route}) => ({
        headerShown: false,
        headerBackButtonDisplayMode: 'minimal',
        // eslint-disable-next-line react/no-unstable-nested-components
        tabBarIcon: ({color, size}) => {
          let iconName;

          if (route.name === SCREEN_NAME.HOME) {
            iconName = 'home';
          } else if (route.name === SCREEN_NAME.MY_ORDER) {
            iconName = 'view-grid';
          } else if (route.name === SCREEN_NAME.CART) {
            iconName = 'cart';
          } else if (route.name === SCREEN_NAME.WISHLIST) {
            iconName = 'cards-heart';
          } else if (route.name === SCREEN_NAME.SIDE_BAR) {
            iconName = 'account';
          }

          return <Icon source={iconName} size={size} color={color} />;
        },

        tabBarActiveTintColor: customColors.primaryContainer,
        tabBarInactiveTintColor: colors.tertiaryBlue,
      })}>
      <Tab.Screen
        options={{title: 'Home'}}
        name={SCREEN_NAME.HOME}
        component={HomeScreen}
      />
      <Tab.Screen
        options={{title: 'Wishlist'}}
        name={SCREEN_NAME.WISHLIST}
        component={WishlistScreen}
      />
      <Tab.Screen
        name={SCREEN_NAME.MY_ORDER}
        options={{title: 'Categories'}}
        component={MyOrderScreen}
      />
      <Tab.Screen
        options={{title: 'Profile'}}
        name={SCREEN_NAME.SIDE_BAR}
        component={SidebarMenu}
      />
      <Tab.Screen
        options={{title: 'Cart'}}
        name={SCREEN_NAME.CART}
        component={OrderConfirmation}
      />
    </Tab.Navigator>
  );
};

export default BottomTabNavigation;
