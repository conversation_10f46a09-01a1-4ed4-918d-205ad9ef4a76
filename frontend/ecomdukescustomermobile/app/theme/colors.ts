import {MD3LightTheme} from 'react-native-paper';

export const colors = {
  ...MD3LightTheme.colors,
  primary: '#9D3191',
  onPrimary: '#FFFFFF',
  secondary: '#F8F9FA',
  tertiary: '#00004F',
  tertiaryBlue: '#5847F9',
  background: '#FFFFFF',
  surface: '#EFF1F2',
  surfaceVariant: '#EFF1F2',
  outline: '#707070',
  outlineVariant: '#F1F1F5',
  error: '#ff2c2c',
  onSurface: '#171725',
  elevation: {
    ...MD3LightTheme.colors.elevation,
    level1: '#EFF1F2',
  },
  gray: {
    light: '#dddddd',
    dark: '#555555',
    medium: '#A4A4A4',
    backGround: '#f1f1f4',
    elevation: '#F8F8F8',
    card: '#EFF1F2',
    text: '#515151',
  },
  navy: {
    deep: '#00004F',
  },
  orange: {
    vibrant: '#FFA500',
  },
  green: {
    success: '#28A745',
  },

  tertiaryContainer: 'red',
  primaryContainer: 'red',
  secondaryContainer: 'red',
  transparent: '#0005',
  favourites: '#e91e63',
};
