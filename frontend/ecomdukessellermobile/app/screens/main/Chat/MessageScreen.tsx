import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {KeyboardAvoidingView, Platform, StyleSheet, View} from 'react-native';
import {
  Bubble,
  BubbleProps,
  GiftedChat,
  IMessage,
  InputToolbar,
  InputToolbarProps,
  Send,
  SendProps,
} from 'react-native-gifted-chat';
import {Icon, IconButton} from 'react-native-paper';
import {SafeAreaView} from 'react-native-safe-area-context';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import {
  ChatScreenNavigationProp,
  MainStackParamList,
} from '../../../navigations/types';
import {ChatHeader} from './components/ChatHeader';
import {RouteProp, useRoute} from '@react-navigation/native';

import {ChatDto, ChatMessage} from '../../../types/chat';
import {useTypedSelector} from '../../../redux/appstore';
import FullScreenLoader from '../../../components/FullScreenLoader';
import {
  useGetChatMessagesQuery,
  useGetSellerChatsQuery,
  useSendChatMessageMutation,
} from '../../../redux/chat/chatApiSlice';
interface ScreenProps {
  navigation: ChatScreenNavigationProp;
  chat: ChatDto;
}

const MessageScreen: React.FC<ScreenProps> = ({navigation, chat}) => {
  const route = useRoute<RouteProp<MainStackParamList, 'chat'>>();
  const {customerId} = route.params;

  const [chatId, setChatId] = useState<string | null>(chat?.id || null);

  const [search] = useState('');

  const {
    data: userChats = [],
    isLoading: userChatLoading,
    refetch: refetchChats,
  } = useGetSellerChatsQuery({
    search,
  });

  const {
    data: messageDtos = [],
    refetch: refetchMessages,
    isLoading: chatMessageLoading,
  } = useGetChatMessagesQuery(chatId ?? '', {
    skip: !chatId,
    pollingInterval: 5000,
  });
  const [sendChatMessage] = useSendChatMessageMutation();
  useEffect(() => {
    if (!customerId || !userChats.length) return;

    const found = userChats.find(c => c.customerId === customerId);
    if (found?.id && found.id !== chatId) {
      setChatId(found.id);
    }
  }, [customerId, userChats, chatId]);

  const currentChat = userChats.find(c => c.id === chatId);
  const user = currentChat?.userData?.userTenant?.user;
  const fullName = [user?.firstName, user?.lastName].filter(Boolean).join(' ');
  const avatarUrl = user?.photoUrl || '';

  const authId = useTypedSelector(s => s.auth.userDetails?.id) ?? 'me';
  const giftedMessages = useMemo(
    () =>
      messageDtos
        .map(dto => mapToGifted(dto, authId))
        .sort((a, b) => a.createdAt.valueOf() - b.createdAt.valueOf()),
    [messageDtos, authId],
  );
  const [localMsgs, setLocalMsgs] = useState<IMessage[]>([]);
  useEffect(() => setLocalMsgs([]), [chatId]);
  const mergedMsgs = GiftedChat.append(giftedMessages, localMsgs);
  const onSend = useCallback(
    async (newMsgs = []) => {
      if (!chatId || !newMsgs.length) return;
      const text = newMsgs[0]?.text;

      setLocalMsgs(prev => GiftedChat.append(prev, newMsgs));

      await sendChatMessage({chatId, message: {message: text}}).unwrap();
    },
    [chatId, sendChatMessage],
  );
  //
  const isAnyLoading = chatMessageLoading || userChatLoading;
  return (
    <SafeAreaView style={styles.safeArea}>
      {isAnyLoading && <FullScreenLoader />}
      <ChatHeader
        onBackPress={() => navigation.goBack()}
        user={{
          name: fullName,
          avatar: avatarUrl,
          online: false,
        }}
      />

      <GiftedChat
        messages={mergedMsgs}
        onSend={onSend}
        user={{_id: authId}}
        renderBubble={renderBubble}
        renderSend={renderSend}
        renderInputToolbar={props => renderInputToolbar(props, refetchMessages)}
        renderAvatar={null}
        placeholder="Type your message..."
        alwaysShowSend
        inverted={false}
        bottomOffset={Platform.OS === 'ios' ? 20 : 0}
      />
      {Platform.OS === 'android' ? null : (
        <KeyboardAvoidingView behavior="padding" keyboardVerticalOffset={80} />
      )}
    </SafeAreaView>
  );
};
export default MessageScreen;
const mapToGifted = (dto: ChatMessage, _id: string): IMessage => ({
  _id: dto.id,
  text: dto.message,
  createdAt: new Date(dto.createdOn),
  user: {
    _id: dto.senderId,
    name: [dto.sender?.firstName, dto.sender?.lastName]
      .filter(Boolean)
      .join(' '),
    avatar: dto.sender?.presignedPhotoUrl ?? '',
  },
});

const renderBubble = (
  props: React.JSX.IntrinsicAttributes & BubbleProps<IMessage>,
) => (
  <Bubble
    {...props}
    wrapperStyle={{
      left: {
        backgroundColor: colors.gray.light,
        paddingVertical: 8,
        paddingHorizontal: 12,
        borderRadius: 16,
        marginVertical: 4,
        marginRight: 50,
        alignSelf: 'flex-start',
      },
      right: {
        backgroundColor: colors.tertiary,
        paddingVertical: 8,
        paddingHorizontal: 12,
        borderRadius: 16,
        marginVertical: 4,
        marginLeft: 50,
        alignSelf: 'flex-end',
      },
    }}
    textStyle={{
      left: {
        color: customColors.textBlack,
        fontSize: 15,
        lineHeight: 20,
      },
      right: {
        color: customColors.white,
        fontSize: 15,
        lineHeight: 20,
      },
    }}
    containerStyle={{
      left: {flex: 1},
      right: {flex: 1},
    }}
  />
);

const renderSend = (
  props: React.JSX.IntrinsicAttributes & SendProps<IMessage>,
) => (
  <Send {...props}>
    <View style={styles.sendButton}>
      <Icon source="send" size={25} color={colors.tertiary} />
    </View>
  </Send>
);

const renderInputToolbar = (
  props: React.JSX.IntrinsicAttributes & InputToolbarProps<IMessage>,
  onRefresh: () => void,
) => (
  <InputToolbar
    {...props}
    containerStyle={styles.inputToolBar}
    renderActions={() => (
      <IconButton
        icon="refresh"
        size={25}
        iconColor={colors.gray.dark}
        onPress={onRefresh}
        style={styles.refreshIcon}
      />
    )}
  />
);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: customColors.white,
  },
  sendButton: {marginRight: 20, marginBottom: 5},
  inputToolBar: {
    borderTopColor: colors.gray.dark,
    paddingVertical: 6,
    marginLeft: 20,
  },
  refreshIcon: {
    alignSelf: 'center',
  },
});
