import {Image, StyleSheet, View} from 'react-native';
import React from 'react';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import {useDispatch} from 'react-redux';
import {unsetCredentials} from '../../redux/auth/authSlice';
import {Card, Text} from 'react-native-paper';
import {DrawerContentScrollView, DrawerItem} from '@react-navigation/drawer';
import {SCREEN_NAME} from '../../constants';
import customColors from '../../theme/customColors';
import {colors} from '../../theme/colors';
import {useTypedSelector} from '../../redux/appstore';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {MainStackParamList} from '../../navigations/types';
import {GoogleSignin} from '@react-native-google-signin/google-signin';
import {Images} from '../../assets/images';

type Props = {
  closeDrawer: () => void;
};
const Sidemenu = ({closeDrawer}: Props) => {
  const dispatch = useDispatch();
  const insets = useSafeAreaInsets();
  const {userDetails} = useTypedSelector(state => state.auth);
  const navigation =
    useNavigation<NativeStackNavigationProp<MainStackParamList>>();

  const onPressScreen = (screenName: keyof MainStackParamList) => {
    navigation.reset({
      index: 1,
      routes: [
        {name: SCREEN_NAME.MAIN_HOME}, // tab home
        {name: screenName},
      ],
    });
    closeDrawer();
  };
  const getPhotoUri = (photo?: string | {path: string}) => {
    if (!photo) return undefined;
    if (typeof photo === 'string') return photo;
    return photo.path;
  };
  const signOutFromGoogle = async () => {
    try {
      const isSignedIn = await GoogleSignin.getCurrentUser(); // Check if the user is signed in

      if (isSignedIn) {
        await GoogleSignin.revokeAccess(); // Optionally revoke access (if required)
        await GoogleSignin.signOut(); // Sign out from Google
      } else {
        console.log('User is not signed in through Google');
      }
    } catch (error) {
      console.error('Error during Google sign-out: ', error);
    }
  };
  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Card
          style={[
            styles.profileCard,
            {
              paddingTop: insets.top + 20,
            },
          ]}
          contentStyle={styles.cardContent}>
          {userDetails?.photoUrl && (
            <Image
              style={styles.profileImage}
              source={{uri: getPhotoUri(userDetails?.photoUrl)!}}
            />
          )}
          <View style={styles.flex1}>
            <Text variant="titleSmall" style={styles.heloText}>
              Hello,
            </Text>
            <Text variant="titleMedium" style={styles.userName}>
              {`${userDetails?.firstName} ${userDetails?.lastName}`}
            </Text>
          </View>
        </Card>
        <DrawerContentScrollView
          contentContainerStyle={{
            paddingTop: 20,
          }}>
          <DrawerItem
            label={'Profile'}
            onPress={() => {
              onPressScreen(SCREEN_NAME.PROFILE_UPDATE);
            }}
          />

          <DrawerItem
            label={'Plans'}
            onPress={() => {
              onPressScreen(SCREEN_NAME.SUBSCRIPTION);
            }}
          />
          <DrawerItem
            label={'Store'}
            onPress={() => {
              onPressScreen(SCREEN_NAME.COMPANY_UPDATE);
            }}
          />
          <DrawerItem
            label={'Firm'}
            onPress={() => {
              onPressScreen(SCREEN_NAME.FIRM_UPDATE);
            }}
          />
          <DrawerItem
            label={'Bank Details'}
            onPress={() => {
              onPressScreen(SCREEN_NAME.BANK_DETAILS);
            }}
          />
          <DrawerItem
            label={'Chat'}
            onPress={() => {
              onPressScreen(SCREEN_NAME.CHAT_LIST);
            }}
          />

          <DrawerItem
            label={'Warehouse'}
            onPress={() => {
              onPressScreen(SCREEN_NAME.WAREHOUSE_LIST);
            }}
          />

          <DrawerItem
            label={'Shipping Settings'}
            onPress={() => {
              onPressScreen(SCREEN_NAME.SHIPPING_DETAIL);
            }}
          />
          <DrawerItem
            label={'Promo Code'}
            onPress={() => {
              onPressScreen(SCREEN_NAME.PROMOCODE_LIST);
            }}
          />
          <DrawerItem
            label={'Change Password'}
            onPress={() => {
              onPressScreen(SCREEN_NAME.CHANGE_PASSWORD);
            }}
          />
          <DrawerItem
            label={'FAQ'}
            onPress={() => {
              onPressScreen(SCREEN_NAME.FAQ);
            }}
          />
          <DrawerItem
            label={'Logout'}
            onPress={() => {
              dispatch(unsetCredentials());
              signOutFromGoogle();
            }}
          />
        </DrawerContentScrollView>
        <Image
          source={Images.logo} // Add your logo
          style={{width: '70%', height: 100, alignSelf: 'center'}} // Adjust size
        />
      </View>
    </SafeAreaView>
  );
};

export default Sidemenu;

const styles = StyleSheet.create({
  cardContent: {
    alignItems: 'flex-start',
    borderRadius: 0,
    flexDirection: 'row',
    padding: 10,
    paddingBottom: 30,
  },
  container: {
    flex: 1,
    // padding: 15
  },
  profileCard: {
    backgroundColor: colors.primary,
    borderRadius: 0,
    marginBottom: 0,
  },
  profileImage: {
    borderRadius: 5,
    height: '100%',
    marginRight: 10,
    width: 75,
  },
  safeArea: {backgroundColor: customColors.white, flex: 1},
  userName: {
    fontSize: 24,
    marginTop: 10,
    color: customColors.white,
    textTransform: 'capitalize',
  },
  heloText: {
    fontSize: 20,
    color: customColors.white,
  },
  flex1: {
    flex: 1,
  },
});
