{"name": "ecomdukessellermobile", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.ts,.tsx", "postinstall": "patch-package"}, "dependencies": {"@10play/tentap-editor": "^0.7.0", "@gorhom/bottom-sheet": "^5", "@notifee/react-native": "^9.1.8", "@react-native-documents/picker": "^10.1.1", "@react-native-documents/viewer": "^1.0.1", "@react-native-firebase/app": "^21.12.1", "@react-native-firebase/messaging": "^21.13.0", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.3.2", "@react-navigation/drawer": "^7.3.0", "@react-navigation/native": "^7.0.18", "@react-navigation/native-stack": "^7.3.2", "@react-navigation/stack": "^7.2.2", "@reduxjs/toolkit": "^2.6.1", "@types/react-redux": "^7.1.34", "country-state-city": "^3.2.1", "dayjs": "^1.11.13", "draftjs-to-html": "^0.9.1", "formik": "^2.4.6", "html-to-text": "^9.0.5", "libphonenumber-js": "^1.12.7", "mime": "^4.0.7", "moment": "^2.30.1", "react": "19.0.0", "react-native": "0.78.1", "react-native-date-picker": "^5.0.12", "react-native-drawer-layout": "^4.1.6", "react-native-element-dropdown": "^2.12.4", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.24.0", "react-native-gifted-chat": "^2.8.1", "react-native-image-crop-picker": "^0.42.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-controller": "^1.17.1", "react-native-mmkv": "^3.2.0", "react-native-otp-entry": "^1.8.4", "react-native-paper": "^5.13.1", "react-native-phone-number-input": "^2.1.0", "react-native-reanimated": "^3.17.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.9.2", "react-native-select-dropdown": "^4.0.1", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.11.2", "react-native-toast-message": "^2.2.1", "react-native-ui-datepicker": "^3.1.2", "react-native-vector-icons": "^10.2.0", "react-native-webview": "^13.13.5", "react-redux": "^9.2.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.1", "@react-native/eslint-config": "0.78.1", "@react-native/metro-config": "0.78.1", "@react-native/typescript-config": "0.78.1", "@types/draftjs-to-html": "^0.8.4", "@types/html-to-text": "^9.0.4", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^29.6.3", "patch-package": "^5.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^3.5.3", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}