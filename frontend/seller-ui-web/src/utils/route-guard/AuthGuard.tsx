'use client';

import { useEffect, useState } from 'react';

// next
import { useRouter } from 'next/navigation';

// project-imports
import Loader from 'components/Loader';

// types
import { usePathname } from 'next/navigation';
import { GuardProps } from 'types/auth';
import { useGetUserQuery } from '../../redux/auth/authApiSlice';
import { useAppSelector } from '../../redux/hooks';
import { getRoute } from './getRoute';
import AuthAlert from 'sections/auth/AuthAlert';
import { useDispatch } from 'react-redux';
import { clearAuthNotice } from 'redux/auth/authSlice';

// ==============================|| AUTH GUARD ||============================== //

export default function AuthGuard({ children }: GuardProps) {
  const dispatch = useDispatch();
  const isLoggedIn = useAppSelector((state) => state.auth.isLoggedIn);
  const AuthNotice = useAppSelector((state) => state.auth.authNotice);
  const [open, setOpen] = useState<boolean>(false);
  const {
    data: user,
    isLoading: isUserLoading,
    isFetching: isUseFetching,
    isUninitialized: userUninitialized,
    refetch
  } = useGetUserQuery(undefined, {
    skip: !isLoggedIn
  });

  useEffect(() => {
    if (isLoggedIn) {
      refetch();
    }
  }, [isLoggedIn, refetch]);

  const currentPath = usePathname();

  const router = useRouter();

  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/login');
    }

    if (isUserLoading || userUninitialized || isUseFetching) {
      return;
    }

    if (user) {
      const pathName = getRoute(user, currentPath);

      if (currentPath !== pathName) {
        router.push(pathName);
        return;
      }
    }

    if (AuthNotice) {
      setOpen(true);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }
  }, [isUserLoading, isLoggedIn, userUninitialized, user, isUseFetching, AuthNotice]);

  if (isUserLoading || userUninitialized) return <Loader />;

  return (
    <>
      <AuthAlert
        message={AuthNotice}
        open={open}
        handleClose={() => {
          dispatch(clearAuthNotice()), setOpen(!open), refetch();
        }}
      />
      {children}
    </>
  );
}
