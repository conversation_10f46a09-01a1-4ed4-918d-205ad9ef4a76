import * as Yup from 'yup';

export const shippingProfileValidation = Yup.object().shape({
  name: Yup.string().required('Profile Name is required').label('Profile Name'),
  sellerId: Yup.string().required('Seller is required').label('Seller'),
  shippingMethodId: Yup.string().required('Shipping Method is required').label('Shipping Method'),
  fallbackMinDeliveryDays: Yup.number()
    .min(0, 'Minimum delivery days cannot be negative')
    .required('Minimum Delivery Days is required')
    .label('Minimum Delivery Days'),
  fallbackMaxDeliveryDays: Yup.number()
    .min(0, 'Maximum delivery days cannot be negative')
    .moreThan(Yup.ref('fallbackMinDeliveryDays'), 'Maximum Delivery Days must be greater than Minimum Delivery Days')
    .required('Maximum Delivery Days is required')
    .label('Maximum Delivery Days'),
  fallbackPrice: Yup.number().min(0, 'Fallback price cannot be negative').required('Fallback Price is required').label('Fallback Price')
});

export const basicShippingChargeValidation = Yup.object().shape({
  stateType: Yup.string().required('State type is required').label('State Type'),
  price: Yup.number().min(0, 'Price cannot be negative').required('Price is required').label('Price')
});

export const dynamicSchema = Yup.lazy((obj) =>
  Yup.object(
    Object.fromEntries(
      Object.keys(obj || {}).map((state) => [
        state,
        Yup.object(
          Object.fromEntries(
            Object.keys(obj[state] || {}).map((range) => [
              range,
              Yup.number().typeError('Must be a number').required('Required').min(0, 'Price cannot be negative')
            ])
          )
        )
      ])
    )
  )
);
