'use client';

import { Box, Grid, Paper, Typography, Stack, List, ListItem, ListItemText } from '@mui/material';

const plans = [
  {
    name: 'FREE PLAN',
    description: 'Suitable For those who sell Finished Products Only',
    features: ['No Extra Fees']
  },
  {
    name: 'BASIC PLAN',
    description: 'Suitable For those who sell Finished Products & Personalisable Products',
    features: [
      'Free till Monthly Sales Value is below Rs 5,000',
      'Rs 129 when Monthly sale exceeds Rs 5,000',
      '+ Rs 211 when Monthly sale exceeds Rs 12,000',
      '+ Rs 211 when Monthly sale exceeds Rs 25,000'
    ]
  },
  {
    name: 'ADVANCED PLAN',
    description: 'Suitable For those who sell Finished Products, Personalisable Products and accept Custom orders',
    features: [
      'Free till Monthly Sales Value is below Rs 5,000',
      'Rs 139 when Monthly sale exceeds Rs 5,000',
      '+ Rs 221 when Monthly sale exceeds Rs 12,000',
      '+ Rs 221 when Monthly sale exceeds Rs 25,000'
    ]
  },
  {
    name: 'PRO PLAN',
    description: 'COMING SOON',
    features: []
  }
];

export default function PlansPricingStatic() {
  return (
    <Box sx={{ px: { xs: 2, sm: 4, md: 8 }, py: 6 }}>
      <Box
        sx={{
          bgcolor: 'white',
          borderRadius: '16px',
          padding: 4,
          mt: 2,
          pl: { xs: 3, sm: 6, md: 10 },
          minHeight: '120vh'
        }}
      >
        <Typography
          variant="h4"
          fontWeight="bold"
          color="#000050"
          gutterBottom
          sx={{
            textAlign: 'left',
            fontSize: { xs: '1.4rem', sm: '1.6rem', md: '2rem' },
            mb: 4
          }}
        >
          Plans Pricing
        </Typography>

        <Grid container spacing={3}>
          {plans.map((plan, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Paper
                elevation={0}
                sx={{
                  border: '1px solid #00004F',
                  borderRadius: '16px',
                  height: '120%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'flex-start'
                }}
              >
                <Box
                  sx={{
                    borderBottom: '1px solid #00004F',
                    textAlign: 'center',
                    py: 2
                  }}
                >
                  <Typography variant="h4" fontWeight="bold" sx={{ color: '#9E3393', textTransform: 'uppercase' }}>
                    {plan.name}
                  </Typography>
                </Box>

                <Box sx={{ px: 2.5, py: 4, flexGrow: 1 }}>
                  {plan.features.length > 0 ? (
                    <>
                      <Stack spacing={1} alignItems="center" mb={3} mt={3} px={1}>
                        <Typography variant="subtitle1" fontWeight="bold" textAlign="center" sx={{ fontSize: '16px' }}>
                          {plan.description}
                        </Typography>
                      </Stack>

                      <List sx={{ py: 0 }}>
                        {plan.features.map((feature, fIdx) => (
                          <ListItem key={fIdx} sx={{ justifyContent: 'center', py: 1.3 }}>
                            <ListItemText
                              primary={feature}
                              primaryTypographyProps={{
                                fontSize: 14,
                                textAlign: 'center'
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </>
                  ) : (
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                        mt: 3
                      }}
                    >
                      <Typography textAlign="center" fontWeight="bold" sx={{ color: '#333', fontSize: 16 }}>
                        COMING SOON
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
}
