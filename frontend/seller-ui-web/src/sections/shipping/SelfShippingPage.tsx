import React, { useState, useMemo } from 'react';
import { Box, Card, CardContent, Typography, Grid, Tabs, Tab, Alert } from '@mui/material';
import MainCard from 'components/MainCard';
import { useGetUserQuery } from 'redux/auth/authApiSlice';
import { useGetShippingProfilesQuery } from 'redux/ecom/shippingApiSlice';
import { useGetSellerStoreBySellerIdQuery } from 'redux/auth/sellerApiSlice';
import { useGetFirmDetailsSellerByIdQuery } from 'redux/app/onboard/firmApiSlice';
import ShippingProfileForm from './ShippingProfile';
import BasicShippingRule from './BasicShippingRule';
import AdvancedShippingChargeTable from './AdvancedShippingChargeTable';
import ProductVariantShippingTable from './ProductVariantShippingCharge';

// Tab panel component
function TabPanel(props: any) {
  const { children, value, index, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== index} id={`shipping-tabpanel-${index}`} aria-labelledby={`shipping-tab-${index}`} {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const SelfShippingPage = () => {
  const [activeTab, setActiveTab] = useState(0);

  // Get user data and shipping profiles
  const { data: userData } = useGetUserQuery();
  const { data: shippingProfiles } = useGetShippingProfilesQuery(
    { sellerId: userData?.profileId ?? '', filter: { limit: 1, order: ['createdOn DESC'] } },
    {
      skip: !userData?.profileId
    }
  );

  const { data: selelrStore } = useGetSellerStoreBySellerIdQuery(userData?.profileId ?? '', {
    skip: !userData?.profileId
  });
  const { data: selelrFirm } = useGetFirmDetailsSellerByIdQuery(userData?.profileId ?? '', {
    skip: !userData?.profileId
  });

  const shippingProfile = useMemo(() => {
    return shippingProfiles?.[0];
  }, [shippingProfiles]);

  // Check if seller has PAN
  const hasPAN = selelrFirm?.panNumber;
  const sellerState = selelrStore?.state;

  const handleTabChange = (_: React.SyntheticEvent, value: number) => {
    setActiveTab(value);
  };

  return (
    <MainCard title="Self-Shipping Settings">
      <Grid container spacing={3}>
        {/* Card 1: Create Shipping Profile */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h4" gutterBottom>
                Shipping Profile
              </Typography>
              <Typography variant="body2" color="textSecondary" paragraph>
                Create a shipping profile with fallback pricing and delivery times.
              </Typography>

              <ShippingProfileForm sellerShippingProfile={shippingProfile} />
            </CardContent>
          </Card>
        </Grid>

        {/* Card 2: Add Shipping Rules */}
        {shippingProfile && userData && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h4" gutterBottom>
                  Shipping Rules
                </Typography>
                <Typography variant="body2" color="textSecondary" paragraph>
                  Configure shipping rules for your products.
                </Typography>

                {!hasPAN && (
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    You need a valid PAN to ship to states outside your registered state ({sellerState}).
                  </Alert>
                )}

                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <Tabs value={activeTab} onChange={handleTabChange}>
                    <Tab label="Basic Shipping" />
                    <Tab label="Advanced Shipping" />
                    <Tab label="Product-Specific Shipping" />
                  </Tabs>
                </Box>

                <TabPanel value={activeTab} index={0}>
                  <BasicShippingRule
                    profileId={shippingProfile.id}
                    sellerId={userData?.profileId}
                    basicShippingCharges={shippingProfile?.basicShippingCharges ?? []}
                  />
                </TabPanel>

                <TabPanel value={activeTab} index={1}>
                  <AdvancedShippingChargeTable
                    profileId={shippingProfile.id}
                    advanceRules={shippingProfile?.advancedShippingCharges ?? []}
                  />
                </TabPanel>

                <TabPanel value={activeTab} index={2}>
                  <ProductVariantShippingTable
                    shippingProfileId={shippingProfile.id}
                    sellerId={userData?.profileId}
                    existingRules={shippingProfile?.productVariantShippingCharges ?? []}
                  />
                </TabPanel>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </MainCard>
  );
};

export default SelfShippingPage;
