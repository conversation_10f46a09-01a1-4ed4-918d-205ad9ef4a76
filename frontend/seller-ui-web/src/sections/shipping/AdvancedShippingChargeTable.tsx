import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Paper,
  IconButton,
  Typography,
  Grid,
  Button,
  MenuItem,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { AdvancedShippingCharge } from 'types/shipping';
import { Add, Trash } from 'iconsax-react';
import { State } from 'country-state-city';
import LoadingButton from 'components/@extended/LoadingButton';
import { dynamicSchema } from 'validation/shipping-settings.validation';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useUpdateShippingProfileMutation } from 'redux/ecom/shippingApiSlice';

interface Props {
  profileId: string;
  advanceRules: AdvancedShippingCharge[];
}

const INITIAL_WEIGHT_RANGES = [
  { min: 100, max: 200 },
  { min: 200, max: 300 },
  { min: 300, max: 400 },
  { min: 400, max: 500 }
];

export default function AdvancedShippingChargeTable({ profileId, advanceRules }: Props) {
  const indianStates = State.getStatesOfCountry('IN');
  const [weightRanges, setWeightRanges] = useState(INITIAL_WEIGHT_RANGES);
  const [selectedStates, setSelectedStates] = useState<string[]>([]);
  const [newState, setNewState] = useState('');
  const [updateShippingProfile, { isLoading: isUpdating }] = useUpdateShippingProfileMutation();

  const formik = useFormik({
    initialValues: {} as Record<string, Record<string, number>>,
    validationSchema: dynamicSchema,
    onSubmit: async () => {
      handleSubmit();
    }
  });

  const handlePriceChange = (stateCode: string, weightKey: string, value: number) => {
    const updated = {
      ...formik.values[stateCode],
      [weightKey]: value
    };
    formik.setFieldValue(stateCode, updated);
  };

  const addNewWeightColumn = () => {
    const last = weightRanges[weightRanges.length - 1];
    setWeightRanges((prev) => [...prev, { min: last.max, max: last.max + 100 }]);
  };

  const handleAddState = () => {
    if (!newState || selectedStates.includes(newState)) return;
    setSelectedStates([...selectedStates, newState]);
    setNewState('');
  };

  const handleRemoveState = (code: string) => {
    setSelectedStates((prev) => prev.filter((state) => state !== code));
    formik.setFieldValue(code, undefined);
  };

  useEffect(() => {
    if (advanceRules.length > 0) {
      const stateWeightMap: Record<string, Record<string, number>> = {};
      const ranges: { min: number; max: number }[] = [];

      advanceRules.forEach((rule) => {
        const rangeKey = `${rule.minWeightGrams}-${rule.maxWeightGrams}`;
        if (!stateWeightMap[rule.state]) {
          stateWeightMap[rule.state] = {};
        }
        stateWeightMap[rule.state][rangeKey] = rule.price;

        if (!ranges.some((r) => r.min === rule.minWeightGrams && r.max === rule.maxWeightGrams)) {
          ranges.push({ min: rule.minWeightGrams, max: rule.maxWeightGrams });
        }
      });

      setSelectedStates(Object.keys(stateWeightMap));
      setWeightRanges(ranges.sort((a, b) => a.min - b.min));
      formik.setValues(stateWeightMap);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advanceRules]);

  async function handleSubmit() {
    const payload: AdvancedShippingCharge[] = [];

    Object.entries(formik.values).forEach(([stateCode, weightMap]) => {
      const stateName = indianStates.find((s) => s.isoCode === stateCode)?.name;
      if (!stateName) return;
      Object.entries(weightMap).forEach(([rangeKey, price]) => {
        const [min, max] = rangeKey.split('-').map(Number);
        if (price !== undefined && price !== null) {
          payload.push({
            shippingProfileId: profileId,
            state: stateName,
            minWeightGrams: min,
            maxWeightGrams: max,
            price,
            isActive: true
          });
        }
      });
    });

    await updateShippingProfile({
      id: profileId,
      body: { advancedShippingCharges: payload }
    }).unwrap();

    openSnackbar({
      open: true,
      message: 'Advance shipping charges updated successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
  }

  return (
    <form onSubmit={formik.handleSubmit}>
      <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
        Advanced Shipping Charges
      </Typography>

      {/* Add State Selector */}
      <Grid container spacing={2} alignItems="center" mb={2}>
        <Grid item xs={5}>
          <FormControl fullWidth>
            <InputLabel>Select State</InputLabel>
            <Select value={newState} onChange={(e) => setNewState(e.target.value)} label="Select State">
              {indianStates.map((state) => (
                <MenuItem key={state.isoCode} value={state.isoCode} disabled={selectedStates.includes(state.isoCode)}>
                  {state.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={2}>
          <Button fullWidth variant="outlined" onClick={handleAddState} disabled={!newState}>
            Add
          </Button>
        </Grid>
      </Grid>

      {/* Table */}
      <TableContainer component={Paper}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>State</TableCell>
              {weightRanges.map((range) => (
                <TableCell key={`${range.min}-${range.max}`} align="center">
                  {range.min}g - {range.max}g
                </TableCell>
              ))}
              <TableCell align="center">
                <IconButton onClick={addNewWeightColumn}>
                  <Add />
                </IconButton>
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {selectedStates.map((stateCode) => {
              const stateName = indianStates.find((s) => s.isoCode === stateCode)?.name || '';
              const weightMap = formik.values[stateCode] || {};
              return (
                <TableRow key={stateCode}>
                  <TableCell>
                    {stateName}
                    <IconButton size="small" onClick={() => handleRemoveState(stateCode)}>
                      <Trash size={16} />
                    </IconButton>
                  </TableCell>
                  {weightRanges.map((range) => {
                    const key = `${range.min}-${range.max}`;
                    const value = weightMap?.[key]?.toString() || '';
                    return (
                      <TableCell key={key}>
                        <TextField
                          type="number"
                          value={value}
                          onChange={(e) => handlePriceChange(stateCode, key, Number(e.target.value))}
                          size="small"
                          fullWidth
                          placeholder="₹"
                          error={!!formik.errors?.[stateCode]?.[key] && formik.touched?.[stateCode]?.[key]}
                          helperText={
                            formik.errors?.[stateCode]?.[key] && formik.touched?.[stateCode]?.[key]
                              ? (formik.errors[stateCode] as any)?.[key]
                              : ''
                          }
                        />
                      </TableCell>
                    );
                  })}
                  <TableCell />
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      <Grid container justifyContent="flex-end" mt={2}>
        <Grid item>
          <LoadingButton type="submit" variant="contained" loading={isUpdating}>
            Save Charges
          </LoadingButton>
        </Grid>
      </Grid>
    </form>
  );
}
