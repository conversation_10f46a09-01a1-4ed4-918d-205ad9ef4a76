import {
  Box,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Checkbox
} from '@mui/material';
import { useEffect, useMemo, useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Add, Trash } from 'iconsax-react';
import LoadingButton from 'components/@extended/LoadingButton';
import { State } from 'country-state-city';
import { ProductVariantShippingCharge } from 'types/shipping';
import { useGetProductVariantsQuery } from 'redux/app/products/productApiSlice';
import { useUpdateShippingProfileMutation } from 'redux/ecom/shippingApiSlice';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';

interface Props {
  shippingProfileId: string;
  sellerId: string;
  existingRules?: ProductVariantShippingCharge[];
}

export default function ProductVariantShippingTable({ shippingProfileId, existingRules = [] }: Props) {
  const [rules, setRules] = useState<ProductVariantShippingCharge[]>([]);
  const newRule: Omit<ProductVariantShippingCharge, 'id'> = {
    shippingProfileId,
    productVariantId: '',
    state: '',
    price: 0,
    isActive: true
  };
  const [updateShippingProfile, { isLoading: isUpdating }] = useUpdateShippingProfileMutation();
  const { data: productVariants } = useGetProductVariantsQuery();

  const states = useMemo(() => State.getStatesOfCountry('IN'), []);

  useEffect(() => {
    setRules(existingRules);
  }, [existingRules]);

  const formik = useFormik({
    initialValues: newRule,
    enableReinitialize: true,
    validationSchema: Yup.object({
      productVariantId: Yup.string().required('Required'),
      state: Yup.string().required('Required'),
      price: Yup.number().positive('Must be > 0').required('Required')
    }),
    onSubmit: () => {
      const exists = rules.find((r) => r.productVariantId === formik.values.productVariantId && r.state === formik.values.state);
      if (exists) return;
      setRules((prev) => [...prev, { ...formik.values }]);
      formik.resetForm();
    }
  });

  const removeRule = (index: number) => {
    const updated = [...rules];
    updated.splice(index, 1);
    setRules(updated);
  };

  const handlePriceChange = (index: number, price: number) => {
    const updated = [...rules];
    updated[index].price = price;
    setRules(updated);
  };

  const handleToggleActive = (index: number) => {
    const updated = [...rules];
    updated[index].isActive = !updated[index].isActive;
    setRules(updated);
  };

  const handleSubmit = async () => {
    await updateShippingProfile({
      id: shippingProfileId,
      body: { productVariantShippingCharges: rules }
    }).unwrap();

    openSnackbar({
      open: true,
      message: 'Advance shipping charges updated successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
  };

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Product Variant Shipping Charges
      </Typography>

      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Product Variant</InputLabel>
              <Select
                name="productVariantId"
                value={formik.values.productVariantId}
                onChange={formik.handleChange}
                error={formik.touched.productVariantId && Boolean(formik.errors.productVariantId)}
              >
                {productVariants?.map((p) => (
                  <MenuItem value={p.id} key={p.id}>
                    {p.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>State</InputLabel>
              <Select
                name="state"
                value={formik.values.state}
                onChange={formik.handleChange}
                error={formik.touched.state && Boolean(formik.errors.state)}
              >
                {states.map((state) => (
                  <MenuItem value={state.name} key={state.isoCode}>
                    {state.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              name="price"
              label="Price (₹)"
              type="number"
              value={formik.values.price}
              onChange={formik.handleChange}
              error={formik.touched.price && Boolean(formik.errors.price)}
              helperText={formik.touched.price && formik.errors.price}
            />
          </Grid>

          <Grid item xs={12} md={1}>
            <LoadingButton type="submit" variant="contained" fullWidth>
              <Add />
            </LoadingButton>
          </Grid>
        </Grid>
      </form>

      <TableContainer component={Paper} sx={{ mt: 3 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>Product Variant</TableCell>
              <TableCell>State</TableCell>
              <TableCell>Price</TableCell>
              <TableCell>Active</TableCell>
              <TableCell align="center">Action</TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {rules.map((rule, index) => {
              const variantName = productVariants?.find((p) => p.id === rule.productVariantId)?.name || '-';
              return (
                <TableRow key={index}>
                  <TableCell>{variantName}</TableCell>
                  <TableCell>{rule.state}</TableCell>
                  <TableCell>
                    <TextField
                      size="small"
                      type="number"
                      value={rule.price}
                      onChange={(e) => handlePriceChange(index, Number(e.target.value))}
                      inputProps={{ min: 0 }}
                    />
                  </TableCell>
                  <TableCell>
                    <Checkbox checked={rule.isActive} onChange={() => handleToggleActive(index)} />
                  </TableCell>
                  <TableCell align="center">
                    <IconButton onClick={() => removeRule(index)}>
                      <Trash />
                    </IconButton>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      <Grid container justifyContent="flex-end" mt={2}>
        <Grid item>
          <LoadingButton variant="contained" onClick={handleSubmit} loading={isUpdating}>
            Save All
          </LoadingButton>
        </Grid>
      </Grid>
    </Box>
  );
}
