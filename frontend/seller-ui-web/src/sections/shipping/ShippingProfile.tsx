import { Box, Checkbox, FormControlLabel, Grid, TextField } from '@mui/material';
import { openSnackbar } from 'api/snackbar';
import LoadingButton from 'components/@extended/LoadingButton';
import { useFormik } from 'formik';
import { useEffect } from 'react';
import { useGetUserQuery } from 'redux/auth/authApiSlice';
import {
  useCreateShippingProfileMutation,
  useGetShippingMethodsQuery,
  useUpdateShippingProfileMutation
} from 'redux/ecom/shippingApiSlice';
import { useAppSelector } from 'redux/hooks';
import { ShippingMethodType, ShippingProfile } from 'types/shipping';
import { SnackbarProps } from 'types/snackbar';
import { shippingProfileValidation } from 'validation/shipping-settings.validation';

interface ShippingProfileProps {
  sellerShippingProfile?: ShippingProfile;
}
export default function ShippingProfileForm({ sellerShippingProfile }: ShippingProfileProps) {
  const { data: shippingMethods, isLoading: isMethodsLoading } = useGetShippingMethodsQuery();
  const [updateShippingProfile, { isLoading: isUpdating }] = useUpdateShippingProfileMutation();
  const [createShippingProfile, { isLoading: isCreating }] = useCreateShippingProfileMutation();
  const isLoggedIn = useAppSelector((state) => state.auth.isLoggedIn);
  const { data: user } = useGetUserQuery(undefined, { skip: !isLoggedIn });

  const formik = useFormik<Omit<ShippingProfile, 'id'>>({
    initialValues: {
      name: sellerShippingProfile?.name || '',
      description: sellerShippingProfile?.description || '',
      isDefault: sellerShippingProfile?.isDefault ?? false,
      isActive: sellerShippingProfile?.isActive ?? true,
      sellerId: sellerShippingProfile?.sellerId || '',
      shippingMethodId: sellerShippingProfile?.shippingMethodId || '',
      fallbackMinDeliveryDays: sellerShippingProfile?.fallbackMinDeliveryDays || 0,
      fallbackMaxDeliveryDays: sellerShippingProfile?.fallbackMaxDeliveryDays || 0,
      fallbackPrice: sellerShippingProfile?.fallbackPrice || 0
    },
    validationSchema: shippingProfileValidation,
    onSubmit: () => {
      handleSubmit();
    }
  });

  useEffect(() => {
    if (sellerShippingProfile) {
      formik.setValues({
        name: sellerShippingProfile.name || '',
        description: sellerShippingProfile.description || '',
        isDefault: sellerShippingProfile.isDefault ?? false,
        isActive: sellerShippingProfile.isActive ?? true,
        sellerId: sellerShippingProfile.sellerId || '',
        shippingMethodId: sellerShippingProfile.shippingMethodId || '',
        fallbackMinDeliveryDays: sellerShippingProfile.fallbackMinDeliveryDays || 0,
        fallbackMaxDeliveryDays: sellerShippingProfile.fallbackMaxDeliveryDays || 0,
        fallbackPrice: sellerShippingProfile.fallbackPrice || 0
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sellerShippingProfile]);

  useEffect(() => {
    if (!isMethodsLoading && shippingMethods?.length) {
      const selfShippingMethod = shippingMethods.find((method) => method.type === ShippingMethodType.SELF_SHIPPING);
      if (selfShippingMethod) {
        formik.setFieldValue('shippingMethodId', selfShippingMethod.id);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shippingMethods, isMethodsLoading]);

  useEffect(() => {
    if (user) {
      formik.setFieldValue('sellerId', user.profileId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const handleSubmit = async () => {
    let message = 'Shipping profile created successfully';
    if (sellerShippingProfile) {
      message = 'Shipping profile updated successfully';
      await updateShippingProfile({
        id: sellerShippingProfile.id,
        body: { ...formik.values, fallbackPrice: Number(formik.values.fallbackPrice) }
      }).unwrap();
    } else {
      await createShippingProfile(formik.values).unwrap();
    }
    openSnackbar({
      open: true,
      message,
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
    formik.resetForm();
  };

  return (
    <form onSubmit={formik.handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            margin="normal"
            label="Name"
            {...formik.getFieldProps('name')}
            error={formik.touched.name && !!formik.errors.name}
            helperText={formik.touched.name && formik.errors.name}
          />
        </Grid>

        <Grid item xs={12}>
          <TextField fullWidth margin="normal" multiline minRows={3} label="Description" {...formik.getFieldProps('description')} />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            margin="normal"
            label="Fallback Min Delivery Days"
            type="number"
            {...formik.getFieldProps('fallbackMinDeliveryDays')}
            error={formik.touched.fallbackMinDeliveryDays && !!formik.errors.fallbackMinDeliveryDays}
            helperText={formik.touched.fallbackMinDeliveryDays && formik.errors.fallbackMinDeliveryDays}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            margin="normal"
            label="Fallback Max Delivery Days"
            type="number"
            {...formik.getFieldProps('fallbackMaxDeliveryDays')}
            error={formik.touched.fallbackMaxDeliveryDays && !!formik.errors.fallbackMaxDeliveryDays}
            helperText={formik.touched.fallbackMaxDeliveryDays && formik.errors.fallbackMaxDeliveryDays}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            margin="normal"
            label="Fallback Price"
            type="number"
            {...formik.getFieldProps('fallbackPrice')}
            error={formik.touched.fallbackPrice && !!formik.errors.fallbackPrice}
            helperText={formik.touched.fallbackPrice && formik.errors.fallbackPrice}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            <FormControlLabel
              control={<Checkbox {...formik.getFieldProps('isDefault')} checked={formik.values.isDefault} />}
              label="Is Default"
            />

            <FormControlLabel
              control={<Checkbox {...formik.getFieldProps('isActive')} checked={formik.values.isActive} />}
              label="Is Active"
            />
          </Box>
        </Grid>

        <Grid item xs={12}>
          <LoadingButton type="submit" variant="contained" sx={{ mt: 2 }} loading={isUpdating || isCreating}>
            Submit
          </LoadingButton>
        </Grid>
      </Grid>
    </form>
  );
}
