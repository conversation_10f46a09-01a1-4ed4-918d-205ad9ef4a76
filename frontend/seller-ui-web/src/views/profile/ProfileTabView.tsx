'use client';

import { useEffect } from 'react';

// next
import { useRouter, usePathname } from 'next/navigation';

// material-ui
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Box from '@mui/material/Box';

// project-imports
import MainCard from 'components/MainCard';
import TabProfile from 'sections/apps/profiles/account/Profile';

import { MenuOrientation } from 'config';
import { handlerActiveItem, useGetMenuMaster } from 'api/menu';

// assets
import { DocumentText, Lock, Money, Notepad2, Profile, Setting3, TableDocument, Truck } from 'iconsax-react';
import { useMediaQuery } from '@mui/system';
import useConfig from 'hooks/useConfig';
import { Toolbar, useTheme } from '@mui/material';
import TabStoreSetting from 'sections/apps/profiles/account/StoreSetting';
import SocialMedia from 'sections/apps/profiles/account/SocialMedia';
import Subscription from 'sections/apps/profiles/account/Subscription';
import Personal from 'sections/apps/profiles/account/Personal';
import FirmDetails from 'sections/apps/profiles/account/FirmDetails';
import ChangePassowrd from 'sections/apps/profiles/account/ChangePassword';
import BankDetails from 'sections/apps/profiles/account/BankDetails';
import SelfShippingPage from 'sections/shipping/SelfShippingPage';

type Props = {
  tab: string;
};

// ==============================|| PROFILE - ACCOUNT ||============================== //

export default function ProfileTabView({ tab }: Props) {
  const theme = useTheme();

  const downLG = useMediaQuery(theme.breakpoints.down('lg'));
  const { menuOrientation } = useConfig();
  const isHorizontal = menuOrientation === MenuOrientation.HORIZONTAL && !downLG;
  const router = useRouter();
  const pathname = usePathname();
  const { menuMaster } = useGetMenuMaster();

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    router.replace(`/account/${newValue}`);
  };

  useEffect(() => {
    if (menuMaster.openedItem !== 'account-profile') handlerActiveItem('account-profile');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  return (
    <>
      <Toolbar
        sx={{
          mt: isHorizontal ? 8 : 'inherit',
          mb: isHorizontal ? 1 : 'inherit'
        }}
      />

      <MainCard border={false}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', width: '100%' }}>
          <Tabs value={tab} onChange={handleChange} variant="scrollable" scrollButtons="auto" aria-label="account profile tab">
            <Tab label="Profile" icon={<Profile />} value="profile" iconPosition="start" />
            <Tab label="Personal" icon={<DocumentText />} value="personal" iconPosition="start" />
            <Tab label="Login & Security" icon={<Lock />} value="login-security" iconPosition="start" />

            <Tab label="Store Setting" icon={<TableDocument />} value="store-setting" iconPosition="start" />
            <Tab label="Firm Details" icon={<Setting3 />} value="firm-details" iconPosition="start" />

            <Tab label="Social Media" icon={<Notepad2 />} value="social-media" iconPosition="start" />
            <Tab label="Bank Details" icon={<Money />} value="bank-details" iconPosition="start" />

            <Tab label="Subscription" icon={<Money />} value="subscription" iconPosition="start" />
            <Tab label="Shipping Settings" icon={<Truck />} value="shipping-settings" iconPosition="start" />
          </Tabs>
        </Box>
        <Box sx={{ mt: 10 }}>
          {tab === 'profile' && <TabProfile />}
          {tab === 'personal' && <Personal />}
          {tab === 'login-security' && <ChangePassowrd />}

          {tab === 'store-setting' && <TabStoreSetting />}
          {tab === 'firm-details' && <FirmDetails />}
          {tab === 'social-media' && <SocialMedia />}
          {tab === 'subscription' && <Subscription />}
          {tab === 'bank-details' && <BankDetails />}
          {tab === 'shipping-settings' && <SelfShippingPage />}
        </Box>
      </MainCard>
    </>
  );
}
