'use client';

import React, { useState, useEffect, useMemo } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  InputLabel,
  TextField,
  CircularProgress,
  Autocomplete,
  FormHelperText
} from '@mui/material';
import { useFormik } from 'formik';

import { useCreateCollectionsMutation, useLazyGetCollectionsQuery } from 'redux/app/collections/collectionApiSlice';
import { useGetTaxCategoriesQuery } from 'redux/app/tax/taxCategoryApiSlice';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { AutoCompleteOption } from 'types/common';
import { Asset } from 'types/product';
import { useCreateAssetMutation, useGetAssetsCountQuery, useGetAssetsQuery } from 'redux/app/products/productApiSlice';

import CollectionAsset from './CollectionAsset';
import { CategoryRequestSchema } from 'validation/category-request.validation';

interface CategoryRequestFormData {
  name: string;
  parentId: string;
  taxCategoryId: string;
  featuredAssetId: string;
}

interface RequestCategoryDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function RequestCategoryDialog({ open, onClose, onSuccess }: RequestCategoryDialogProps) {
  const [collectionInput, setCollectionInput] = useState('');
  const [, setTaxCategoryInput] = useState('');
  const [getCollection, { data: collections, isLoading: collectionLoading }] = useLazyGetCollectionsQuery();
  const { data: taxCategories, isLoading: taxCategoryLoading } = useGetTaxCategoriesQuery();
  const [createCollection] = useCreateCollectionsMutation();

  const [selectedAssets, setSelectedAssets] = useState<{ id: string; preview: string }[]>([]);
  const [openGallery, setOpenGallery] = useState(false);

  const [page, setPage] = useState(0);
  const limit = 10;
  const { data: totalAssets } = useGetAssetsCountQuery();
  const { data: paginatedAssets = [], refetch: refetchAssets } = useGetAssetsQuery({
    order: ['createdOn DESC'],
    limit,
    skip: page * limit
  });
  const [addAsset, { isLoading: isUploading }] = useCreateAssetMutation();

  const [assets, setAssets] = useState<Asset[]>([]);
  const hasMore = (assets.length || 0) < (totalAssets?.count || 0);

  useEffect(() => {
    if (open) {
      getCollection({ where: { name: { ilike: `%${collectionInput}%` } } });
    }
  }, [collectionInput, open]);

  useEffect(() => {
    if (paginatedAssets?.length) {
      setAssets((prev) => [...prev, ...paginatedAssets]);
    }
  }, [paginatedAssets]);

  const collectionOptions: AutoCompleteOption[] = useMemo(() => {
    return collections?.map((item) => ({ label: item.name, value: item.id })) || [];
  }, [collections]);

  const taxCategoryOptions: AutoCompleteOption[] = useMemo(() => {
    return (
      taxCategories
        ?.map((item) => ({ label: item.name, value: item.id }))
        .filter((item): item is AutoCompleteOption => Boolean(item.value)) ?? []
    );
  }, [taxCategories]);

  const formik = useFormik<CategoryRequestFormData>({
    initialValues: {
      name: '',
      parentId: '',
      taxCategoryId: '',
      featuredAssetId: ''
    },
    validationSchema: CategoryRequestSchema,
    onSubmit: async (values) => {
      await createCollection(values).unwrap();
      openSnackbar({
        open: true,
        message: 'Category request submitted successfully',
        variant: 'alert',
        alert: { color: 'success' }
      } as SnackbarProps);
      formik.resetForm();
      setSelectedAssets([]);
      onClose();
      onSuccess?.();
    }
  });

  const handleFileChange = async (file: File) => {
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    const asset = (await addAsset(formData).unwrap()) as unknown as Asset;
    refetchAssets();
    // Set selected asset and featuredAssetId
    setSelectedAssets([{ id: asset.id, preview: asset.previewUrl }]);
    formik.setFieldValue('featuredAssetId', asset.id);
  };

  const handleFeatureAssetChange = (id: string) => {
    formik.setFieldValue('featuredAssetId', id);
  };

  const setFeaturedAssetId = (id: string) => {
    formik.setFieldValue('featuredAssetId', id);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle
        sx={{
          position: 'sticky',
          top: 0,
          zIndex: 2,
          backgroundColor: 'background.paper',
          borderBottom: '1px solid',
          borderColor: 'divider'
        }}
      >
        Request New Category
      </DialogTitle>{' '}
      <form onSubmit={formik.handleSubmit}>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <InputLabel sx={{ mb: 1 }}>Category Name*</InputLabel>
              <TextField
                fullWidth
                name="name"
                placeholder="Enter category name"
                value={formik.values.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
              />
            </Grid>

            <Grid item xs={12}>
              <InputLabel sx={{ mb: 1 }}>Parent Category</InputLabel>
              <Autocomplete
                freeSolo
                options={collectionOptions}
                value={collectionOptions.find((opt) => opt.value === formik.values.parentId) || null}
                onInputChange={(_, newInputValue) => setCollectionInput(newInputValue)}
                onChange={(_, newValue) => {
                  const parentId = typeof newValue === 'string' ? newValue : (newValue?.value ?? '');
                  formik.setFieldValue('parentId', parentId);
                }}
                loading={collectionLoading}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Search parent category"
                    name="parentId"
                    onBlur={formik.handleBlur}
                    error={formik.touched.parentId && Boolean(formik.errors.parentId)}
                    helperText={formik.touched.parentId && formik.errors.parentId}
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {collectionLoading && <CircularProgress color="inherit" size={20} />}
                          {params.InputProps.endAdornment}
                        </>
                      )
                    }}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <InputLabel sx={{ mb: 1 }}>Tax Category</InputLabel>
              <Autocomplete
                freeSolo
                options={taxCategoryOptions}
                value={taxCategoryOptions.find((opt) => opt.value === formik.values.taxCategoryId) || null}
                onInputChange={(_, newInputValue) => setTaxCategoryInput(newInputValue)}
                onChange={(_, newValue) => {
                  const taxCategoryId = typeof newValue === 'string' ? newValue : (newValue?.value ?? '');
                  formik.setFieldValue('taxCategoryId', taxCategoryId);
                }}
                loading={taxCategoryLoading}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Search tax category"
                    name="taxCategoryId"
                    onBlur={formik.handleBlur}
                    error={formik.touched.taxCategoryId && Boolean(formik.errors.taxCategoryId)}
                    helperText={formik.touched.taxCategoryId && formik.errors.taxCategoryId}
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {taxCategoryLoading && <CircularProgress color="inherit" size={20} />}
                          {params.InputProps.endAdornment}
                        </>
                      )
                    }}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <CollectionAsset
                assets={assets}
                openGallery={openGallery}
                toggleGallery={() => setOpenGallery(!openGallery)}
                handleFileChange={handleFileChange}
                isAssetUploading={isUploading}
                setSelectedAssets={setSelectedAssets}
                selectedAssets={selectedAssets}
                handleFeatureAssetChange={handleFeatureAssetChange}
                featuredAssetId={formik.values.featuredAssetId}
                setFeaturedAssetId={setFeaturedAssetId} // ✅ pass to component
                allowMultiple={false}
                featuredAsset={
                  selectedAssets[0] ? { id: selectedAssets[0].id, previewUrl: selectedAssets[0].preview } : { id: '', previewUrl: '' }
                }
                isEdit={false}
                hasMore={hasMore}
                loadMoreAssets={() => setPage((prev) => prev + 1)}
                assetsCount={totalAssets?.count ?? 0}
              />
              {formik.touched.featuredAssetId && formik.errors.featuredAssetId && (
                <FormHelperText error>{formik.errors.featuredAssetId}</FormHelperText>
              )}
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions
          sx={{
            position: 'sticky',
            bottom: 0,
            zIndex: 2,
            backgroundColor: 'background.paper',
            borderTop: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Button onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="contained" disabled={formik.isSubmitting}>
            Submit Request
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}
