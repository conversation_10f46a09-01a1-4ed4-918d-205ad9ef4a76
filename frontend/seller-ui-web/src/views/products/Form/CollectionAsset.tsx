'use client';

import { <PERSON>, But<PERSON>, <PERSON>A<PERSON>, CardContent, Chip, Grid, ImageList, ImageListItem, Modal, Stack, Typography } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import Image from 'next/image';
import { Dispatch, FC, SetStateAction, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';

import { Asset } from 'types/product';
import MainCard from 'components/MainCard';
import { AddAsset } from './AddAsset';
import Loader from 'components/Loader';
import CropImageModal from 'components/third-party/CropModal';

interface Props {
  imageUrl?: string;
  altText?: string;
  openGallery: boolean;
  assets: Asset[];
  toggleGallery: () => void;
  handleFileChange: (e: File) => void;
  isAssetUploading: boolean;
  setSelectedAssets: Dispatch<SetStateAction<{ id: string; preview: string }[]>>;
  selectedAssets: { id: string; preview: string }[];
  handleFeatureAssetChange: (id: string) => void;
  featuredAssetId: string;
  allowMultiple?: boolean;
  featuredAsset: { id: string; previewUrl: string };
  isEdit: boolean;
  assetsCount: number;
  hasMore: boolean;
  loadMoreAssets: () => void;
  setFeaturedAssetId: (id: string) => void; // ✅ New prop
}

const CollectionAsset: FC<Props> = ({
  imageUrl,
  altText,
  openGallery,
  assets,
  toggleGallery,
  handleFileChange,
  isAssetUploading,
  setSelectedAssets,
  selectedAssets,
  handleFeatureAssetChange,
  featuredAssetId,
  featuredAsset,
  isEdit,
  assetsCount,
  hasMore,
  loadMoreAssets,
  setFeaturedAssetId // ✅ Destructure new prop
}) => {
  const [cropModalOpen, setCropModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [, setFileToCrop] = useState<File | null>(null);

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setSelectedImage(reader.result as string);
        setFileToCrop(file);
        setCropModalOpen(true);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleToggle = (id: string, preview: string) => {
    const isSelected = selectedAssets.some((item) => item.id === id);
    const updatedAssets = isSelected ? [] : [{ id, preview }];
    setSelectedAssets(updatedAssets);

    // ✅ Sync with Formik
    if (!isSelected) {
      setFeaturedAssetId(id);
    } else {
      setFeaturedAssetId('');
    }
  };

  const handleImageCancel = () => {
    if (isEdit) {
      setSelectedAssets([{ id: featuredAsset.id, preview: featuredAsset.previewUrl }]);
    } else {
      setSelectedAssets([]);
    }
    toggleGallery();
  };

  return (
    <MainCard title="Media">
      <Box>
        {!!selectedAssets.length && (
          <ImageList sx={{ width: 600 }} cols={4} rowHeight={164}>
            {selectedAssets.map((item) => (
              <ImageListItem
                key={item.id}
                sx={{
                  position: 'relative',
                  border: '2px solid #1976d2',
                  borderRadius: 1,
                  cursor: 'pointer',
                  overflow: 'hidden',
                  '&:hover .overlay': { opacity: 1 }
                }}
              >
                {featuredAssetId === item.id && (
                  <Chip label="Featured" color="success" sx={{ position: 'absolute', top: 0, right: 0, zIndex: 2 }} />
                )}
                <Image src={item.preview} alt={item.id} loading="lazy" fill style={{ objectFit: 'cover' }} />
                <Box
                  className="overlay"
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    backgroundColor: 'rgba(0, 0, 0, 0.6)',
                    opacity: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'column',
                    transition: 'opacity 0.3s ease-in-out',
                    zIndex: 1
                  }}
                >
                  <Button
                    variant="text"
                    color="secondary"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFeatureAssetChange(item.id);
                    }}
                  >
                    Mark as Feature Asset
                  </Button>
                  <Button
                    variant="text"
                    color="secondary"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleToggle(item.id, item.preview);
                    }}
                  >
                    Remove
                  </Button>
                </Box>
              </ImageListItem>
            ))}
          </ImageList>
        )}

        <Stack>
          {!selectedAssets.length && (
            <Box sx={{ width: 100, height: 100, borderRadius: 10, position: 'relative' }}>
              <Image
                src={imageUrl ?? '/assets/images/icons/image_placeholder.png'}
                alt={altText ?? 'Product Image'}
                height={100}
                width={100}
              />
            </Box>
          )}
          <LoadingButton variant="outlined" onClick={toggleGallery} loading={isAssetUploading}>
            Add Media
          </LoadingButton>
        </Stack>

        <Modal open={openGallery}>
          <MainCard
            title="Choose Media"
            modal
            darkTitle
            content={false}
            secondary={<AddAsset handleChange={handleImageSelect} />}
            sx={{
              position: 'absolute' as const,
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: '90%',
              maxWidth: '600px',
              maxHeight: '90vh',
              overflowY: 'auto'
            }}
          >
            <CardContent sx={{ p: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={7}>
                  <Box
                    sx={{
                      width: '100%',
                      height: 300,
                      position: 'relative',
                      borderRadius: 2,
                      overflow: 'hidden',
                      bgcolor: 'grey.100',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    {selectedAssets.length > 0 ? (
                      <>
                        <Image src={selectedAssets[0].preview} alt={selectedAssets[0].id} fill style={{ objectFit: 'contain' }} priority />
                        {featuredAssetId === selectedAssets[0].id && (
                          <Chip label="Featured" color="success" size="small" sx={{ position: 'absolute', top: 8, right: 8, zIndex: 2 }} />
                        )}
                      </>
                    ) : (
                      <Typography color="text.secondary">Select an image</Typography>
                    )}
                  </Box>
                </Grid>

                <Grid item xs={12} md={5}>
                  <Box
                    id="scrollableAssetsBox"
                    sx={{
                      height: 300,
                      overflowY: 'auto',
                      borderRadius: 2,
                      p: 1,
                      bgcolor: 'background.paper'
                    }}
                  >
                    <InfiniteScroll
                      dataLength={assetsCount}
                      loader={<Loader />}
                      scrollThreshold={0.9}
                      next={loadMoreAssets}
                      hasMore={hasMore}
                      scrollableTarget="scrollableAssetsBox"
                    >
                      <ImageList cols={1} gap={10} sx={{ width: '60%', ml: 4 }}>
                        {assets.map((item) => {
                          const isSelected = selectedAssets.some((asset) => asset.id === item.id);

                          return (
                            <ImageListItem key={item.id} sx={{ borderRadius: 1, overflow: 'hidden', width: '100%' }}>
                              <Box
                                onClick={() => handleToggle(item.id, item.previewUrl)}
                                sx={{
                                  position: 'relative',
                                  cursor: 'pointer',
                                  aspectRatio: '1',
                                  border: isSelected ? '2px solid' : '2px solid transparent',
                                  borderColor: isSelected ? 'primary.main' : 'transparent',
                                  borderRadius: 1,
                                  overflow: 'hidden',
                                  '&:hover': { boxShadow: 2 }
                                }}
                              >
                                <Image
                                  src={item.previewUrl}
                                  alt={item.name}
                                  style={{ objectFit: 'cover' }}
                                  loading="lazy"
                                  width={100}
                                  height={100}
                                />
                                {isSelected && (
                                  <Box
                                    sx={{
                                      position: 'absolute',
                                      inset: 0,
                                      bgcolor: 'rgba(25, 118, 210, 0.3)',
                                      display: 'flex',
                                      alignItems: 'flex-end',
                                      justifyContent: 'flex-end',
                                      p: 1
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        width: 20,
                                        height: 20,
                                        borderRadius: '50%',
                                        bgcolor: 'common.white',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: 'primary.main'
                                      }}
                                    >
                                      ✓
                                    </Box>
                                  </Box>
                                )}
                              </Box>
                            </ImageListItem>
                          );
                        })}
                      </ImageList>
                    </InfiniteScroll>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>

            <CardActions sx={{ justifyContent: 'flex-end', p: 2, gap: 1 }}>
              <Button onClick={handleImageCancel} color="inherit" variant="text">
                Cancel
              </Button>
              <LoadingButton variant="contained" onClick={toggleGallery} loading={isAssetUploading} disabled={!selectedAssets.length}>
                {selectedAssets.length ? `Select ${selectedAssets.length} Media` : 'Select Media'}
              </LoadingButton>
            </CardActions>
          </MainCard>
        </Modal>

        {selectedImage && (
          <CropImageModal
            open={cropModalOpen}
            onClose={() => setCropModalOpen(false)}
            onCropComplete={(blob: any) => {
              if (blob) {
                handleFileChange(blob);
              }
            }}
            imageSrc={selectedImage}
          />
        )}
      </Box>
    </MainCard>
  );
};

export default CollectionAsset;
