'use client';

// next
import Image from 'next/image';

// material-ui
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

// project-imports
import { useDispatch } from 'react-redux';

import { unsetCredentials } from 'redux/auth/authSlice';
import { useRouter } from 'next/navigation';
import { apiSlice } from 'redux/apiSlice';
import { ApiTagTypes } from 'redux/types';

// assets
const error404 = '/assets/images/maintenance/img-error-404.svg';

// ==============================|| ERROR 404 ||============================== //

export default function Error404Page() {
  const theme = useTheme();
  const matchDownSM = useMediaQuery(theme.breakpoints.down('sm'));
  const dispatch = useDispatch();
  const router = useRouter();

  const handleLogout = () => {
    dispatch(unsetCredentials());
    dispatch(apiSlice.util.invalidateTags([ApiTagTypes.User]));
    router.push('/login');
  };

  return (
    <Grid
      container
      spacing={10}
      direction="column"
      alignItems="center"
      justifyContent="center"
      sx={{ minHeight: '100vh', pt: 2, pb: 1, overflow: 'hidden' }}
    >
      <Grid item xs={12}>
        <Stack direction="row">
          <Grid item>
            <Box sx={{ width: 300 }}>
              <Image
                src={error404}
                alt="able-pro"
                width={matchDownSM ? 350 : 396}
                height={matchDownSM ? 325 : 370}
                style={{
                  maxWidth: '100%',
                  height: 'auto'
                }}
              />
            </Box>
          </Grid>
        </Stack>
      </Grid>
      <Grid item xs={12}>
        <Stack spacing={2} justifyContent="center" alignItems="center">
          <Typography variant="h1">Un Authorized</Typography>
          <Typography color="text.secondary" align="center">
            You are not authorized to access this page or application. <br />
            Please contact your administrator if you believe this is a mistake.
          </Typography>
          <Button
            variant="contained"
            onClick={() => {
              handleLogout();
            }}
          >
            logout
          </Button>
        </Stack>
      </Grid>
    </Grid>
  );
}
