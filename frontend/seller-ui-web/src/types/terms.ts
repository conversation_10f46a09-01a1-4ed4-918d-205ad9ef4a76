import { LegalCategory, Legals, LegalVisibility } from 'enums/legal-category.enum';
import { SupportStatus, TicketCategory } from 'enums/legal-category.enum copy';

export interface LegalType {
  id?: string;
  data: string;
  category: LegalCategory;
  type: Legals;
  visibility?: LegalVisibility;
  createdBy?: string;
  modifiedBy?: string;
  createdOn?: string;
  modifiedOn?: string;
  deleted?: boolean;
}

export interface CreateTicketDto {
  category: TicketCategory;
  title: string;
  description: string;
  attachments?: string;
  priority?: number;
  status: SupportStatus;
  assignee?: string;
}
export interface HelpItem {
  question: string;
  answer: string;
  category: string;
  visibility: number;
  status: number;
}
export interface SupportContactInfo {
  id: string;
  supportEmail: string;
  supportPhone: string;
  status: number;
  visibility: number;
}
