export enum ShippingMethodType {
  ECOMDUKES = 'ECOMDUKES',
  SELF_SHIPPING = 'SELF_SHIPPING'
}

export interface ShippingMethod {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  type: ShippingMethodType;
}

export enum BasicShippingStateType {
  IN_STATE = 'IN_STATE',
  OUT_STATE = 'OUT_STATE'
}

export interface ShippingProfile {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
  isActive: boolean;
  sellerId: string;
  shippingMethodId: string;
  fallbackMinDeliveryDays: number;
  fallbackMaxDeliveryDays: number;
  fallbackPrice: number;
  basicShippingCharges?: BasicShippingCharge[];
  advancedShippingCharges?: AdvancedShippingCharge[];
  productVariantShippingCharges?: ProductVariantShippingCharge[];
}

export interface BasicShippingCharge {
  id?: string;
  shippingProfileId: string;
  stateType: BasicShippingStateType;
  price: number;
  isActive: boolean;
}

export interface AdvancedShippingCharge {
  id?: string;
  shippingProfileId: string;
  state: string;
  minWeightGrams: number;
  maxWeightGrams: number;
  price: number;
  isActive: boolean;
}

export interface ProductVariantShippingCharge {
  id?: string;
  shippingProfileId: string;
  productVariantId: string;
  state: string;
  price: number;
  isActive: boolean;
}

export interface ProductVariant {
  id: string;
  name: string;
  sku: string;
  weight?: number;
}
