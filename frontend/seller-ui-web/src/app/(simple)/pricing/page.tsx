'use client';
import { useRef } from 'react';
import FAQSection from 'sections/landing/LandingFaqSection.tsx';
import BottomSection from 'sections/pricing-info/NoteSection';
import SupportServiceFees from 'sections/pricing-info/PlansSection';
import PricingFeatureTable from 'sections/pricing-info/PricingFeature';
import PricingInfoPage from 'sections/pricing-info/PricingInfoPage';
import PricingPlansPage from 'sections/pricing-info/PricingPlans';
import FixedFeeSection from 'sections/pricing-info/PricingRange';

export default function PricingPage() {
  const featureSectionRef = useRef<HTMLDivElement>(null);

  return (
    <>
      <PricingInfoPage />
      <FixedFeeSection />
      <PricingPlansPage />
      <div ref={featureSectionRef}>
        <PricingFeatureTable />
      </div>
      <SupportServiceFees />
      <BottomSection />
      <FAQSection />
    </>
  );
}
