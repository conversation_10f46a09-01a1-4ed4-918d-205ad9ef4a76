import { ApiSliceIdentifier } from 'enums/api.enum';
import { LegalVisibility } from 'enums/legal-category.enum';
import { FaqStatus } from 'enums/legal-category.enum copy';
import { apiSlice } from 'redux/apiSlice';
import { CreateTicketDto, HelpItem, SupportContactInfo } from 'types/terms';

export const supportApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    createSupportTicket: builder.mutation<void, CreateTicketDto>({
      query: (formData) => ({
        url: '/tickets',
        method: 'POST',
        body: formData,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    getHelp: builder.query<HelpItem[], void>({
      query: () => ({
        url: '/helps',
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              and: [
                {
                  visibility: {
                    inq: [LegalVisibility.SELLER, LegalVisibility.ALL]
                  }
                },
                {
                  status: FaqStatus.ACTIVE
                }
              ]
            },
            order: ['createdOn DESC']
          })
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),

    getSupportContact: builder.query<SupportContactInfo[], void>({
      query: () => ({
        url: `/support`,
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              and: [
                {
                  visibility: {
                    inq: [LegalVisibility.SELLER, LegalVisibility.ALL]
                  }
                },
                {
                  status: FaqStatus.ACTIVE
                }
              ]
            },
            order: ['createdOn DESC'],
            limit: 1
          })
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    })
  })
});

export const { useCreateSupportTicketMutation, useGetHelpQuery, useGetSupportContactQuery } = supportApiSlice;
