export enum LegalCategory {
  Seller = 'Seller',
  Customer = 'Customer',
  Product = 'Product',
  Order = 'Order',
  Refund = 'Refund'
}
export enum LegalVisibility {
  ALL = 0,
  ADMIN = 1,
  SELLER = 2,
  CUSTOMER = 3,
  SellOnEcomdukes = 4
}
export enum Legals {
  TermsofUse = 'Terms of Use',
  PrivacyPolicy = 'Privacy Policy',
  AffiliatePolicy = ' Affiliate Policy',
  ReturnRefundPolicy = 'ReturnRefundPolicy',
  InfringementPolicy = 'Infringement Policy',
  Agreements = 'Agreements',
  Licence = 'Licence',
  Disclaimer = 'Disclaimer',
  Guideline = 'Guideline'
}
export enum FaqStatus {
  ACTIVE = 0,
  INACTIVE = 1
}
export enum SupportStatus {
  Open = 'Open',
  InProgress = 'InProgress',
  Resolved = 'Resolved',
  Closed = 'Closed'
}
export enum TicketCategory {
  Orders = 'Orders',
  Payments = 'Payments',
  Refunds = 'Refunds',
  Shipping = 'Shipping',
  Returns = 'Returns',
  ProductInquiry = 'ProductInquiry',
  TechnicalIssue = 'TechnicalIssue',
  Account = 'Account',
  Other = 'Other'
}
