export interface DiscountCondition {
  id?: string;
  thresholdAmount: number;
  discountType: 'FLAT' | 'PERCENT';
  discountValue: number;
  conditionType: 'BASIC' | 'ADDITIONAL' | 'APP_ONLY';
  isAppOnly: boolean;
  discountId?: string;
}

export interface Discount {
  id?: string;
  name: string;
  description?: string;
  isActive: boolean;
  startDate?: string;
  endDate?: string;
  usageLimitPerUser?: number;
  combinable: boolean;
  createdOn?: string;
  discountConditions?: DiscountCondition[];
}

export interface CreateDiscountDto extends Discount {
  conditions: DiscountCondition[];
}

interface AppliedDiscount {
  conditionId: string;
  discountValue: number;
  discountType: string;
}

export interface DiscountEligibilityResponse {
  isEligible: boolean;
  appliedDiscount: AppliedDiscount | null;
  nextTierMessage: string;
  discountBreakdownMessage: string;
}
