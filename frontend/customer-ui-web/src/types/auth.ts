import {ReactNode} from 'react';

// ==============================|| TYPES - AUTH  ||============================== //
export interface UserFormData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  password: string;
  confirmPassword: string;
  countryCode?: string;
  subscribeToNewsletter?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  submit?: any;
}

export type SignupDto = {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  subscribeToNewsletter?: boolean;
  fcmToken?: string | null;
};
export interface TokenResponse {
  code: string;
  accessToken: string;
  refreshToken: string;
  expires: number;
  pubnubToken: string;
  message: string;
}

export type GuardProps = {
  children: ReactNode;
};
export type LoginResponse = {
  code: string;
  error?: {message: {message: string}};
};
export type UserProfile = {
  id?: string;
  email?: string;
  avatar?: string;
  image?: string;
  name?: string;
  role?: string;
  tier?: string;
};

export interface AuthProps {
  isLoggedIn: boolean;
  isInitialized?: boolean;
  user?: UserProfile | null;
  token?: string | null;
}

export interface AuthActionProps {
  type: string;
  payload?: AuthProps;
}

export interface JWTDataProps {
  userId: string;
}

export type JWTContextType = {
  isLoggedIn: boolean;
  isInitialized?: boolean;
  user?: UserProfile | null | undefined;
  logout: () => void;
  login: (email: string, password: string) => Promise<void>;
  register: (
    email: string,
    password: string,
    firstName: string,
    lastName: string,
  ) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: VoidFunction;
};
export interface FacetValue {
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  name: string;
  code: string;
  facetId: string;
  facet: string;
  foreignKey: string;
}

export interface Facet {
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  name: string;
  code: string;
  facetValues: FacetValue[];
}
export interface ChangePasswordRequest {
  username: string;
  password: string;
  oldPassword: string;
  refreshToken: string;
}
