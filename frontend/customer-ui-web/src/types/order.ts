import {Customer} from './customer';
import {ProductVariant} from './product';
import {Review} from './review';

export interface Order {
  id: string;
  customerId: string;
  orderId?: string;
  orderReferenceId?: string;
  totalAmount: number;
  discountAmount: number;
  payableAmount: number;
  currency: string;
  status: string;
  cartId: string;
  orderLineItems: OrderLineItem[];
  promoCodeId: string;
  createdOn: string;
  customer?: Customer;
  invoiceId?: string;
  shippingAddress: Address;
  billingAddress: Address;
}
export interface Address {
  id: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  addressType: string;
  customerId: string;
  locality: string;
  name: string;
  phoneNumber: string;
  landmark: string;
  alternativePhoneNumber: string;
}
export interface OrderLineItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  orderId: string;
  productVariantId: string;
  warehouseId: string;
  productVariant?: ProductVariant;
  review?: Review;
  order: Order;
}
