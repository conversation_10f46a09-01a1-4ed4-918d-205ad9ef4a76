'use client';

import {Box, Grid} from '@mui/material';
import {useParams} from 'next/navigation';
import {Legals} from 'types/legal-category.enum';
import LegalContent from 'views/legals/LegalContent';

const slugToTypeMap: Record<string, Legals> = {
  terms: Legals.TermsofUse,
  privacy: Legals.PrivacyPolicy,
  affiliate: Legals.AffiliatePolicy,
  refund: Legals.ReturnRefundPolicy,
  infringement: Legals.InfringementPolicy,
  agreements: Legals.Agreements,
  licence: Legals.Licence,
  disclaimer: Legals.Disclaimer,
  guidelines: Legals.Guideline,
};

const FooterLegalPage = () => {
  const {slug} = useParams();
  const legalType = slugToTypeMap[slug as string];

  return (
    <Grid container spacing={3} px={6}>
      <Box sx={{px: 6, mt: 4, width: '100%'}}>
        <LegalContent type={legalType} />
      </Box>
    </Grid>
  );
};

export default FooterLegalPage;
