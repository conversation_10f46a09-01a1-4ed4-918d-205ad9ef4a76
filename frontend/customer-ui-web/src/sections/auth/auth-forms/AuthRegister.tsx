'use client';

import {SyntheticEvent, useEffect, useState} from 'react';

// material-ui
import FormHelperText from '@mui/material/FormHelperText';
import Grid from '@mui/material/Grid';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';

// third-party
import {Formik} from 'formik';

// project-imports
import AnimateButton from 'components/@extended/AnimateButton';
import IconButton from 'components/@extended/IconButton';
import {strengthColor, strengthIndicator} from 'utils/password-strength';

// types
import {StringColorProps} from 'types/password';

// assets
import LoadingButton from 'components/@extended/LoadingButton';
import {useApiErrorHandler} from 'hooks/useApiErrorHandler';
import {Eye, EyeSlash} from 'iconsax-react';
import {useRouter} from 'next/navigation';
import {
  useCreateSignUpTokenMutation,
  useSignUpMutation,
} from '../../../redux/auth/authApiSlice';
import {UserFormData} from 'types/auth';
import {registerValidation} from 'validation/registration.validation';
import {FormControlLabel} from '@mui/material';
import {Checkbox} from '@mui/material';
import Link from 'next/link';
import useFcmToken from 'components/NotificationProvider';

// ============================|| JWT - REGISTER ||============================ //

export default function AuthRegister() {
  const handleError = useApiErrorHandler();
  const fcmToken = useFcmToken();
  const [
    signupTokenApi,
    {isLoading: tokenLoading, error: tokenError, reset: tokenReset},
  ] = useCreateSignUpTokenMutation();
  const [signupApi, {isLoading, error: signupError, reset: signupReset}] =
    useSignUpMutation();
  const router = useRouter();

  const [, setLevel] = useState<StringColorProps>();
  const [, setConfirmLevel] = useState<StringColorProps>();
  const [isAgreed, setIsAgreed] = useState(false);

  const [showPassword, setShowPassword] = useState(false);
  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };
  const [confirmshowPassword, setConfirmShowPassword] = useState(false);
  const handleClickConfirmShowPassword = () => {
    setConfirmShowPassword(!confirmshowPassword);
  };

  const handleMouseDownPassword = (event: SyntheticEvent) => {
    event.preventDefault();
  };
  const handleMouseDownConfirmPassword = (event: SyntheticEvent) => {
    event.preventDefault();
  };

  const changePassword = (value: string) => {
    const temp = strengthIndicator(value);
    setLevel(strengthColor(temp));
  };

  useEffect(() => {
    changePassword('');
  }, []);
  const changeConfirmPassword = (value: string) => {
    const temp = strengthIndicator(value);
    setConfirmLevel(strengthColor(temp));
  };

  useEffect(() => {
    changeConfirmPassword('');
  }, []);

  const handleSubmit = async (values: UserFormData) => {
    const countryCode = values.countryCode;
    const token = await signupTokenApi(values.email).unwrap();
    const response = await signupApi({
      token: token.code,
      signupData: {
        firstName: values.firstName,
        lastName: values.lastName,
        phoneNumber: `${countryCode?.replace('+', '')}${values.phoneNumber}`,
        email: values.email,
        password: values.password,
        subscribeToNewsletter: values.subscribeToNewsletter,
        fcmToken: fcmToken,
      },
    });
    if (!response.error) {
      router.push('/welcome');
    }
  };

  useEffect(() => {
    if (signupError) {
      handleError(signupError);
      signupReset();
    }
    if (tokenError) {
      handleError(tokenError);
      tokenReset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [signupError, handleError, tokenError]);
  return (
    <Formik<UserFormData>
      initialValues={{
        firstName: '',
        lastName: '',
        phoneNumber: '',
        email: '',
        confirmPassword: '',
        password: '',
        countryCode: '+91',
        subscribeToNewsletter: false,
        submit: null,
      }}
      validationSchema={registerValidation}
      onSubmit={async values => {
        handleSubmit(values);
      }}
    >
      {({
        errors,
        handleBlur,
        handleChange,
        handleSubmit,
        isSubmitting,

        touched,
        values,
      }) => (
        <form noValidate onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="firstName-signup">First Name</InputLabel>
                <OutlinedInput
                  id="firstName-signup"
                  type="firstName"
                  value={values.firstName}
                  name="firstName"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  fullWidth
                  error={Boolean(touched.firstName && errors.firstName)}
                  autoComplete="false"
                />
              </Stack>
              {touched.firstName && errors.firstName && (
                <FormHelperText error id="helper-text-firstName-signup">
                  {errors.firstName}
                </FormHelperText>
              )}
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="lastName-signup">Last Name</InputLabel>
                <OutlinedInput
                  id="lastName-signup"
                  type="text"
                  value={values.lastName}
                  name="lastName"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  autoComplete="false"
                  fullWidth
                  error={Boolean(touched.lastName && errors.lastName)}
                />
              </Stack>
              {touched.lastName && errors.lastName && (
                <FormHelperText error id="helper-text-lastName-signup">
                  {errors.lastName}
                </FormHelperText>
              )}
            </Grid>

            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="phoneNumber-signup">
                  Phone Number
                </InputLabel>
                <Stack direction="row" spacing={1}>
                  <OutlinedInput
                    id="phoneNumber-signup"
                    type="tel"
                    value={values.phoneNumber}
                    name="phoneNumber"
                    onBlur={handleBlur}
                    autoComplete="false"
                    onChange={handleChange}
                    fullWidth
                    placeholder="Enter phone number"
                    error={Boolean(touched.phoneNumber && errors.phoneNumber)}
                    startAdornment={
                      <InputAdornment position="start">
                        {values.countryCode}{' '}
                      </InputAdornment>
                    }
                  />
                </Stack>

                {touched.phoneNumber && errors.phoneNumber && (
                  <FormHelperText error id="helper-text-phoneNumber-signup">
                    {errors.phoneNumber}
                  </FormHelperText>
                )}
              </Stack>
            </Grid>

            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="email-signup">Email Address</InputLabel>
                <OutlinedInput
                  fullWidth
                  error={Boolean(touched.email && errors.email)}
                  id="email-signup"
                  type="email"
                  value={values.email}
                  name="email"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  autoComplete="false"
                  inputProps={{}}
                />
              </Stack>
              {touched.email && errors.email && (
                <FormHelperText error id="helper-text-email-signup">
                  {errors.email}
                </FormHelperText>
              )}
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="password-signup">Password</InputLabel>
                <OutlinedInput
                  fullWidth
                  error={Boolean(touched.password && errors.password)}
                  id="password-signup"
                  type={showPassword ? 'text' : 'password'}
                  value={values.password}
                  name="password"
                  onBlur={handleBlur}
                  onChange={e => {
                    handleChange(e);
                    changePassword(e.target.value);
                  }}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleClickShowPassword}
                        onMouseDown={handleMouseDownPassword}
                        edge="end"
                        color="secondary"
                      >
                        {showPassword ? <Eye /> : <EyeSlash />}
                      </IconButton>
                    </InputAdornment>
                  }
                  autoComplete="false"
                  inputProps={{}}
                />
              </Stack>
              {touched.password && errors.password && (
                <FormHelperText error id="helper-text-password-signup">
                  {errors.password}
                </FormHelperText>
              )}
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <InputLabel htmlFor="password-signup">
                  {' '}
                  Confirm Password
                </InputLabel>
                <OutlinedInput
                  fullWidth
                  error={Boolean(
                    touched.confirmPassword && errors.confirmPassword,
                  )}
                  id="password-signup"
                  type={confirmshowPassword ? 'text' : 'password'}
                  value={values.confirmPassword}
                  name="confirmPassword"
                  onBlur={handleBlur}
                  onChange={e => {
                    handleChange(e);
                    changeConfirmPassword(e.target.value);
                  }}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleClickConfirmShowPassword}
                        onMouseDown={handleMouseDownConfirmPassword}
                        edge="end"
                        color="secondary"
                      >
                        {confirmshowPassword ? <Eye /> : <EyeSlash />}
                      </IconButton>
                    </InputAdornment>
                  }
                  inputProps={{}}
                  autoComplete="false"
                />
              </Stack>
              {touched.confirmPassword && errors.confirmPassword && (
                <FormHelperText error id="helper-text-password-signup">
                  {errors.confirmPassword}
                </FormHelperText>
              )}
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={values.subscribeToNewsletter}
                    onChange={handleChange}
                    color="primary"
                    name="subscribeToNewsletter"
                  />
                }
                label="Keep me informed with the latest news, new products, deals, and updates"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isAgreed}
                    onChange={e => setIsAgreed(e.target.checked)}
                    color="primary"
                    name="terms"
                  />
                }
                label={
                  <span>
                    I agree to the{' '}
                    <Link href="/terms-condition" target="_blank">
                      <strong
                        style={{textDecoration: 'underline', cursor: 'pointer'}}
                      >
                        Terms & Conditions
                      </strong>
                    </Link>{' '}
                    and{' '}
                    <Link href="/privacy-policy" target="_blank">
                      <strong
                        style={{textDecoration: 'underline', cursor: 'pointer'}}
                      >
                        Privacy Policy
                      </strong>
                    </Link>
                    .
                  </span>
                }
              />
            </Grid>

            <Grid
              item
              xs={12}
              sx={{
                display: 'flex',
                justifyContent: 'flex-end',
                alignItems: 'center',
                mt: 2,
              }}
            >
              <AnimateButton>
                <LoadingButton
                  disableElevation
                  disabled={isSubmitting || !isAgreed}
                  size="large"
                  type="submit"
                  variant="contained"
                  color="primary"
                  loading={tokenLoading || isLoading}
                  sx={{minWidth: 200, px: 4}}
                >
                  Continue
                </LoadingButton>
              </AnimateButton>
            </Grid>
          </Grid>
        </form>
      )}
    </Formik>
  );
}
