import {IFilterWithKeyword} from 'types/filter';
import {ReviewStatus} from 'types/review';

export const DEFAULT_CURRENCY = 'INR';
export const CURRENCY_MAP: Record<string, string> = {
  [DEFAULT_CURRENCY]: '₹',
};

export const getSearchParam = (
  isLoggedIn: boolean,
  profileId?: string,
): IFilterWithKeyword => ({
  include: [
    {
      relation: 'featuredAsset',
      scope: {
        fields: {preview: true, id: true},
      },
    },
    {
      relation: 'product',
      scope: {
        fields: {description: true, id: true},
        
      },
    },
    {
      relation: 'productVariantPrice',
      scope: {
        fields: {
          price: true,
          mrp: true,
          currencyCode: true,
        },
      },
    },
    ...(isLoggedIn
      ? [
          {
            relation: 'wishlist',
            scope: {
              where: {
                deleted: false,
                customerId: profileId,
              },
              fields: {id: true},
            },
          },
        ]
      : []),
    {
      relation: 'reviews',
      scope: {
        fields: {
          rating: true,
        },
        where: {
          status: ReviewStatus.APPROVED,
        },
      },
    },
  ],
  fields: {
    name: true,
    id: true,
    featuredAssetId: true,
    productId: true,
  },
});
