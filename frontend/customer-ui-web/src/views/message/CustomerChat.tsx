'use client';

import {useEffect, useRef, useState} from 'react';
import {useTheme, styled, Theme} from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import {
  Grid,
  Stack,
  Dialog,
  Pop<PERSON>,
  <PERSON><PERSON>se,
  TextField,
  ClickAwayListener,
  Box,
} from '@mui/material';

import MainCard from 'components/MainCard';
import IconButton from 'components/@extended/IconButton';
import SimpleBar from 'components/third-party/SimpleBar';
import {PopupTransition} from 'components/@extended/Transitions';

import {ThemeMode} from 'config';
import {
  EmojiHappy,
  Image as ImageIcon,
  Paperclip,
  Refresh,
  Send,
  VolumeHigh,
} from 'iconsax-react';

import ChatDrawer from './ChatDrawer';
import ChatHeader from './ChatHeader';
import UserDetails from './UserDetails';

import {
  useGetChatMessagesQuery,
  useSendChatMessageMutation,
  useGetUserChatsQuery,
  useCreateChatMutation,
} from 'redux/chat/chatApiSlice';
import {useGetUserQuery} from 'redux/auth/authApiSlice';

import {ChatDto} from 'types/chat';
import {useSearchParams} from 'next/navigation';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import ChatHistory from './ChatHistory';
import EmojiPicker, {EmojiClickData, SkinTones} from 'emoji-picker-react';
dayjs.extend(relativeTime);

const drawerWidth = 320;

const Main = styled('main', {
  shouldForwardProp: (prop: string) => prop !== 'open',
})(({theme, open}: {theme: Theme; open: boolean}) => ({
  flexGrow: 1,
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.shorter,
  }),
  marginLeft: `-${drawerWidth}px`,
  [theme.breakpoints.down('lg')]: {
    paddingLeft: 0,
    marginLeft: 0,
  },
  ...(open && {
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.shorter,
    }),
    marginLeft: 0,
  }),
}));

const ChatWidget: React.FC<{chat: ChatDto | null}> = ({chat: initialChat}) => {
  const theme = useTheme();
  const matchDownSM = useMediaQuery(theme.breakpoints.down('lg'));
  const matchDownMD = useMediaQuery(theme.breakpoints.down('md'));

  const [selectedChat, setSelectedChat] = useState<ChatDto | null>(initialChat);
  const chatId = selectedChat?.id ?? null;

  const [emailDetails, setEmailDetails] = useState(false);
  const [anchorElEmoji, setAnchorElEmoji] = useState<null | HTMLElement>(null);
  const [openChatDrawer, setOpenChatDrawer] = useState(true);
  const [message, setMessage] = useState('');

  const textInput = useRef<HTMLInputElement | null>(null);
  const bottomRef = useRef<HTMLDivElement | null>(null);

  const searchParams = useSearchParams();
  const sellerId = searchParams.get('sellerId');

  const [search, setSearch] = useState('');
  const {data: userChats = [], refetch: refetchChats} = useGetUserChatsQuery({
    search,
  });

  const [createChat] = useCreateChatMutation();
  const [sendChatMessage] = useSendChatMessageMutation();

  const {data: fetchedMessages = [], refetch} = useGetChatMessagesQuery(
    chatId ?? '',
    {skip: !chatId},
  );

  useEffect(() => {
    if (!sellerId || !userChats.length) return;

    const existingChat = userChats.find(
      (chat: ChatDto) => chat.sellerId === sellerId,
    );

    if (existingChat?.id && chatId !== existingChat.id) {
      setSelectedChat(existingChat);
    } else if (!existingChat && !chatId) {
      createChat({sellerId})
        .unwrap()
        .then(newChat => {
          setSelectedChat(newChat);
          refetchChats();
        })
        .catch(err => console.error('Failed to create chat', err));
    }
  }, [sellerId, userChats.length]);

  const handleUserChange = () => setEmailDetails(prev => !prev);
  const handleDrawerOpen = () => setOpenChatDrawer(prev => !prev);

  const handleOnEmojiButtonClick = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    setAnchorElEmoji(anchorElEmoji ? null : event.currentTarget);
  };

  const emojiOpen = Boolean(anchorElEmoji);
  const emojiId = emojiOpen ? 'simple-popper' : undefined;
  const handleCloseEmoji = () => setAnchorElEmoji(null);

  const handleOnSend = async () => {
    if (!message.trim() || !chatId) return;
    await sendChatMessage({
      chatId,
      message: {message: message.trim()},
    }).unwrap();

    setMessage('');
    refetch();
  };

  const handleEnter = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleOnSend();
    }
  };

  const onEmojiClick = (emojiObject: EmojiClickData) => {
    setMessage(message + emojiObject.emoji);
  };

  const {data: userDetails} = useGetUserQuery();
  const customerId = userDetails?.id;

  const handleReloadMessages = () => {
    refetch();
  };

  useEffect(() => {
    if (bottomRef.current) {
      bottomRef.current.scrollIntoView({behavior: 'smooth'});
    }
  }, [fetchedMessages]);

  useEffect(() => {
    setOpenChatDrawer(!matchDownSM);
  }, [matchDownSM]);

  return (
    <Box sx={{display: 'flex', overflow: 'hidden'}}>
      <ChatDrawer
        openChatDrawer={openChatDrawer}
        handleDrawerOpen={handleDrawerOpen}
        setSelectedChat={setSelectedChat}
        chat={selectedChat}
        selectedChatId={chatId}
        search={search}
        setSearch={setSearch}
      />

      <Main theme={theme} open={openChatDrawer}>
        <Grid container height={1}>
          <Grid
            item
            xs={12}
            md={emailDetails ? 8 : 12}
            xl={emailDetails ? 9 : 12}
          >
            <MainCard
              content={false}
              sx={{
                height: 1,
                bgcolor:
                  theme.palette.mode === ThemeMode.DARK
                    ? 'dark.main'
                    : 'grey.50',
                pt: 2,
                pl: 2,
                borderRadius: emailDetails ? '0' : '0 12px 12px 0',
              }}
            >
              <Grid container spacing={2} height={1}>
                <Grid item xs={12}>
                  <ChatHeader
                    loading={false}
                    user={selectedChat?.userData?.userTenant?.user ?? null}
                    preSignedPhotoUrl={
                      selectedChat?.userData?.presignedPhotoUrl
                    }
                    lastUpdated={
                      selectedChat?.modifiedOn ?? selectedChat?.createdOn
                    }
                    handleDrawerOpen={handleDrawerOpen}
                  />
                </Grid>
                <Grid item xs={12}>
                  <SimpleBar sx={{height: 'calc(100vh - 416px)', p: 2}}>
                    <ChatHistory
                      messages={fetchedMessages}
                      currentUserId={customerId ?? ''}
                    />
                    <div ref={bottomRef} />
                  </SimpleBar>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sx={{borderTop: `1px solid ${theme.palette.divider}`}}
                >
                  <Stack>
                    <TextField
                      inputRef={textInput}
                      fullWidth
                      multiline
                      rows={2}
                      placeholder="Your Message..."
                      value={message}
                      onChange={e => setMessage(e.target.value)}
                      onKeyDown={handleEnter}
                      variant="outlined"
                    />
                    <Stack
                      direction="row"
                      justifyContent="space-between"
                      alignItems="center"
                    >
                      <Stack direction="row" sx={{py: 2, ml: -1}}>
                        <IconButton
                          color="secondary"
                          onClick={handleReloadMessages}
                        >
                          <Refresh />
                        </IconButton>
                        <IconButton
                          aria-describedby={emojiId}
                          onClick={handleOnEmojiButtonClick}
                          sx={{opacity: 0.5}}
                          size="medium"
                          color="secondary"
                        >
                          <EmojiHappy />
                        </IconButton>
                        <Popper
                          id={emojiId}
                          open={emojiOpen}
                          anchorEl={anchorElEmoji}
                          disablePortal
                        >
                          <ClickAwayListener onClickAway={handleCloseEmoji}>
                            <MainCard elevation={8} content={false}>
                              <EmojiPicker
                                onEmojiClick={onEmojiClick}
                                defaultSkinTone={SkinTones.DARK}
                              />
                            </MainCard>
                          </ClickAwayListener>
                        </Popper>
                        <IconButton
                          sx={{opacity: 0.5}}
                          size="medium"
                          color="secondary"
                        >
                          <Paperclip />
                        </IconButton>
                        <IconButton
                          sx={{opacity: 0.5}}
                          size="medium"
                          color="secondary"
                        >
                          <ImageIcon />
                        </IconButton>
                        <IconButton
                          sx={{opacity: 0.5}}
                          size="medium"
                          color="secondary"
                        >
                          <VolumeHigh />
                        </IconButton>
                      </Stack>
                      <IconButton
                        color="primary"
                        onClick={handleOnSend}
                        size="large"
                        sx={{mr: 1.5}}
                        disabled={!message.trim()}
                      >
                        <Send />
                      </IconButton>
                    </Stack>
                  </Stack>
                </Grid>
              </Grid>
            </MainCard>
          </Grid>

          <Grid
            item
            xs={12}
            md={4}
            xl={3}
            sx={{display: emailDetails ? 'flex' : 'none'}}
          >
            <Collapse
              orientation="horizontal"
              in={emailDetails && !matchDownMD}
            >
              <UserDetails
                user={{id: '1', name: 'John Doe'}}
                onClose={handleUserChange}
              />
            </Collapse>
          </Grid>

          <Dialog
            TransitionComponent={PopupTransition}
            open={matchDownMD && emailDetails}
            onClose={handleUserChange}
          >
            <UserDetails
              user={{id: '1', name: 'John Doe'}}
              onClose={handleUserChange}
            />
          </Dialog>
        </Grid>
      </Main>
    </Box>
  );
};

export default ChatWidget;
