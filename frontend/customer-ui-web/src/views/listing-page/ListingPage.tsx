'use client';

import {useMemo, useEffect, useState, useCallback} from 'react';
import {Grid} from '@mui/material';
import {useSearchParams} from 'next/navigation';
import {getSearchParam} from 'constants/product';
import {useAppSelector} from 'redux/hooks';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {useFiltersQuery, useLazySearchQuery} from 'redux/search/searchApiSlice';
import {useGetCollectionsQuery} from 'redux/ecom/collectionApiSlice';

import {FilterValue, IFilterWithKeyword} from 'types/filter';
import ProductFilters from 'views/products/ProductFilters';
import ProductList from 'views/products/ProductListing';
import FilterSortProducts from 'views/products/QuikFilter';
import ScrollableCategoryList from 'views/products/ProductSubCategory';

interface Category {
  id: string;
  label: string;
  img: string;
  parentId?: string;
}

export default function ListingPage() {
  const searchParams = useSearchParams();
  const searchQuery = searchParams.get('search')?.toLowerCase() || '';
  const facetValueIdsParam = searchParams.get('facetValueIds');
  const collectionIdsParam = searchParams.get('collectionIds');

  const facetValueIds = facetValueIdsParam?.split(',').filter(Boolean);
  const collectionIds = collectionIdsParam?.split(',').filter(Boolean);

  const [appliedFilters, setAppliedFilters] = useState<FilterValue[]>([]);
  const [sortOrder, setSortOrder] = useState<
    'featured' | 'lowToHigh' | 'highToLow'
  >('featured');

  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const {data: user} = useGetUserQuery(undefined, {skip: !isLoggedIn});
  const searchParam = getSearchParam(isLoggedIn, user?.profileId);

  const {data: filters} = useFiltersQuery(
    {
      keyword: searchQuery,
      facetValueIds,
      collectionIds,
    },
    {
      skip: false,
    },
  );
  console.log('🚀 ~ ListingPage ~ filters:', filters);

  const collectionFilter = useMemo(() => {
    return filters?.find(f =>
      f.values?.some(v => v.metadata?.previewUrl && v.metadata?.parentId),
    );
  }, [filters]);

  const baseCategories: Category[] = useMemo(() => {
    const collections = collectionFilter?.values;
    if (!collections) return [];
    return collections.map(col => ({
      id: col.value,
      label: col.label,
      img: col.metadata?.previewUrl ?? '',
      parentId: col.metadata?.parentId ?? undefined, // this will be `undefined` if null
    }));
  }, [collectionFilter]);

  const uniqueParentIds = useMemo(() => {
    return Array.from(new Set(baseCategories.map(c => c.id).filter(Boolean)));
  }, [baseCategories]);

  const {data: subCollections} = useGetCollectionsQuery({
    where: {
      parentId: {inq: uniqueParentIds},
    },
    include: [
      {
        relation: 'featuredAsset',
        scope: {
          fields: {preview: true, id: true},
        },
      },
    ],
    fields: {
      name: true,
      id: true,
      parentId: true,
      featuredAssetId: true,
    },
  });

  const subCategories: Category[] = useMemo(() => {
    if (!subCollections) return [];
    return subCollections.map(col => ({
      id: col.id,
      label: col.name,
      img: col.featuredAsset?.preview ?? '',
      parentId: col.parentId,
    }));
  }, [subCollections]);

  const allCategories: Category[] = [...baseCategories, ...subCategories];

  const [fetchProductsApi, {data: products, isLoading, isFetching}] =
    useLazySearchQuery();

  useEffect(() => {
    const params: IFilterWithKeyword = {...searchParam};

    if (searchQuery) params.keyword = searchQuery;
    if (facetValueIds?.length) params.facetValueIds = facetValueIds;
    if (collectionIds?.length) params.collectionIds = collectionIds;

    params.where = appliedFilters.length ? prePareFilter() : undefined;
    console.log('🚀 ~ useEffect ~  params.where:', params.where);

    fetchProductsApi(params).unwrap();
  }, [
    searchQuery,
    appliedFilters,
    sortOrder,
    facetValueIdsParam,
    collectionIdsParam,
  ]);

  const sortedProducts = useMemo(() => {
    if (!products) return [];

    if (sortOrder === 'lowToHigh') {
      return [...products].sort(
        (a, b) =>
          parseFloat(a.productVariantPrice?.price ?? '0') -
          parseFloat(b.productVariantPrice?.price ?? '0'),
      );
    }

    if (sortOrder === 'highToLow') {
      return [...products].sort(
        (a, b) =>
          parseFloat(b.productVariantPrice?.price ?? '0') -
          parseFloat(a.productVariantPrice?.price ?? '0'),
      );
    }

    return products;
  }, [products, sortOrder]);

  const toggleFilter = useCallback((selectedFilter: FilterValue) => {
    setAppliedFilters(prev => {
      const exists = prev.some(
        f =>
          f.label === selectedFilter.label && f.value === selectedFilter.value,
      );

      return exists
        ? prev.filter(
            f =>
              !(
                f.label === selectedFilter.label &&
                f.value === selectedFilter.value
              ),
          )
        : [...prev, selectedFilter];
    });
  }, []);

  const facetFilters = useMemo(() => {
    if (!filters) return [];
    return filters.filter(item => item.isFacet).flatMap(item => item.values);
  }, [filters]);

  // const prePareFilter = () => {
  //   const ids = appliedFilters.flatMap(item => item.productVariantIds || []);
  //   return ids.length ? {id: {inq: ids}} : undefined;
  // };
  const prePareFilter = () => {
    const priceFilter = appliedFilters.find(f => f.label === 'Price');
    const otherFilters = appliedFilters.filter(f => f.label !== 'Price');

    const ids = otherFilters.flatMap(item => item.productVariantIds || []);
    const priceWhere =
      priceFilter?.value && priceFilter.value.includes('-')
        ? {
            productVariantPrice: {
              price: {
                between: priceFilter.value.split('-').map(Number),
              },
            },
          }
        : undefined;

    const where: any = {};
    if (ids.length) where.id = {inq: ids};
    if (priceWhere) where.and = [where, priceWhere];

    return where;
  };

  return (
    <Grid container spacing={2} px={2}>
      <Grid item xs={12}>
        <ScrollableCategoryList categories={allCategories} />
      </Grid>

      <Grid item xs={12}>
        <FilterSortProducts
          facets={facetFilters}
          sortOrder={sortOrder}
          onSortChange={setSortOrder}
        />
      </Grid>

      <Grid item xs={12} container spacing={2}>
        <Grid item xs={12} sm={4} md={3}>
          <ProductFilters
            filters={filters ?? []}
            onToggleFilter={toggleFilter}
            appliedFilters={appliedFilters}
          />
        </Grid>

        <Grid item xs={12} sm={8} md={9}>
          <ProductList
            products={sortedProducts}
            isLoading={isLoading}
            isFetching={isFetching}
          />
        </Grid>
      </Grid>
    </Grid>
  );
}
