import {Box, Grid, Typography, Divider, CardMedia} from '@mui/material';
import {Order} from 'types/order';

type OrderSummaryProps = {
  order: Order;
};

const OrderSummary = ({order}: OrderSummaryProps) => {
  const orderItems = order?.orderLineItems ?? [];

  return (
    <Grid
      container
      spacing={4}
      sx={{
        px: {xs: 2, sm: 4, md: 8, lg: 12, xl: 15},
        pb: 3,
      }}
    >
      {/* Order List */}
      <Grid item xs={12} md={7} mt={{xs: 2, md: 4}}>
        <Typography
          variant="h6"
          sx={{
            fontWeight: 700,
            fontSize: {xs: '1rem', sm: '1.1rem', md: '1.2rem'},
            color: '#00004F',
            mb: 1,
            ml: {xs: 0, sm: 2, md: 4, lg: 8},
          }}
        >
          Order List
        </Typography>
        <Divider sx={{mb: 2, borderColor: '#00004F'}} />

        {orderItems.map(item => (
          <Grid
            container
            spacing={2}
            alignItems="center"
            key={item.id}
            sx={{mb: 2}}
          >
            <Grid item>
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  bgcolor: '#f5f5f5',
                  borderRadius: 3,
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                {/* <Image
                  src={
                    item.productVariant?.featuredAsset?.previewUrl ??
                    '/assets/images/placeholder.png'
                  }
                  alt={item.productVariant?.name ?? 'Product'}
                  width={60}
                  height={60}
                /> */}
                <CardMedia
                  component="img"
                  sx={{
                    width: {xs: '100%', sm: 100},
                    height: 60,
                    borderRadius: 2,
                    objectFit: 'contain',
                    cursor: 'pointer',
                  }}
                  image={item?.productVariant?.featuredAsset?.previewUrl ?? ''}
                  alt={item?.productVariant?.name}
                />
              </Box>
            </Grid>

            <Grid item xs>
              <Typography
                fontWeight={600}
                fontSize={{xs: '1.1rem', sm: '1.2rem', md: '1.3rem'}}
                color={'#515151'}
              >
                {item.productVariant?.name}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                fontSize={{xs: '0.9rem', sm: '1rem'}}
              >
                SKU: {item.productVariant?.sku}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                fontSize={{xs: '0.9rem', sm: '1rem'}}
                mt={1}
              >
                #{item.productVariant?.id} | Qty :{' '}
                {item.quantity.toString().padStart(2, '0')}
              </Typography>
            </Grid>

            <Grid item>
              <Typography
                fontWeight={600}
                color={'#515151'}
                fontSize={{xs: '1.2rem', md: '1.5rem'}}
              >
                ₹ {(item.unitPrice * item.quantity).toFixed(2)}
              </Typography>
            </Grid>
          </Grid>
        ))}
      </Grid>

      {/* Order Summary */}
      <Grid item xs={12} md={5} mt={{xs: 1, md: 4}}>
        <Box
          sx={{
            border: '1px solid black',
            borderRadius: 3,
            p: {xs: 2, sm: 3},
            height: '100%',
            bgcolor: '#fff',
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontWeight: 700,
              color: '#00004F',
              mb: 1,
              fontSize: {xs: '1rem', sm: '1.2rem'},
            }}
          >
            Order Summary
          </Typography>
          <Divider sx={{mb: 2, borderColor: '#00004F'}} />

          {[
            [
              'Item(s) Total [MRP]',
              `₹ ${Number(order.totalAmount).toFixed(2)}`,
            ],
            [
              'Special Discount',
              `- ₹ ${Number(order.discountAmount).toFixed(2)}`,
            ],
            ['Delivery Charge', '₹ 0.00'],
            ['Total Payable', `₹ ${Number(order.payableAmount).toFixed(2)}`],
          ].map(([label, value], idx) => (
            <Grid
              container
              justifyContent="space-between"
              key={idx}
              sx={{
                mb: 1,
                borderBottom:
                  label === 'Delivery Charge' ? '1px solid black' : 'none',
                pb: label === 'Delivery Charge' ? 1 : 0,
              }}
            >
              <Typography
                variant="body2"
                color={'#515151'}
                fontWeight={700}
                fontSize="1rem"
              >
                {label} -
              </Typography>
              <Typography
                variant="body2"
                fontWeight={800}
                color={'#515151'}
                fontSize="1rem"
              >
                {value}
              </Typography>
            </Grid>
          ))}

          <Grid
            container
            justifyContent="space-between"
            alignItems="center"
            sx={{mt: 2}}
          >
            <Typography variant="body2" fontSize="1rem" color="#515151">
              Estimated delivery
            </Typography>
            <Typography
              variant="body2"
              fontWeight={800}
              fontSize="1rem"
              color="#515151"
            >
              25 May – 08 Jun
            </Typography>
          </Grid>
        </Box>
      </Grid>
    </Grid>
  );
};

export default OrderSummary;
