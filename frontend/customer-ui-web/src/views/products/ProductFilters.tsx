'use client';
import React, {useState, useEffect, useRef} from 'react';
import {
  Card,
  CardContent,
  Typography,
  Checkbox,
  FormGroup,
  FormControlLabel,
  Button,
  Drawer,
  Box,
  useMediaQuery,
  useTheme,
  IconButton,
  Slider,
  MenuItem,
  Select,
} from '@mui/material';
import {ArrowLeft} from 'iconsax-react';
import {FilterGroup, FilterValue} from 'types/filter';

interface ProductFiltersProps {
  filters: FilterGroup[];
  onToggleFilter: (filter: FilterValue) => void;
  appliedFilters: FilterValue[];
}

const ProductFilters: React.FC<ProductFiltersProps> = ({
  filters,
  onToggleFilter,
  appliedFilters,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [sliderValues, setSliderValues] = useState<
    Record<string, [number, number]>
  >({});
  const hasInitialized = useRef(false);

  useEffect(() => {
    if (!hasInitialized.current) {
      const newSliderValues: Record<string, [number, number]> = {};
      filters.forEach(filter => {
        if (filter.metadata?.type === 'slider') {
          const applied = appliedFilters.find(f => f.label === filter.label);
          if (applied) {
            const [min, max] = applied.value.split('-').map(Number);
            newSliderValues[filter.label] = [min, max];
          } else {
            newSliderValues[filter.label] = [
              filter.metadata.min,
              filter.metadata.max,
            ];
          }
        }
      });
      setSliderValues(newSliderValues);
      hasInitialized.current = true;
    }
  }, [filters, appliedFilters]);

  function hasSliderMetadata(filter: FilterGroup): filter is FilterGroup & {
    metadata: {type: 'slider'; min: number; max: number};
  } {
    return (
      !!filter.metadata &&
      filter.metadata.type === 'slider' &&
      typeof filter.metadata.min === 'number' &&
      typeof filter.metadata.max === 'number'
    );
  }

  const isOptionChecked = (label: string, value: string) => {
    return appliedFilters.some(f => f.label === label && f.value === value);
  };

  const handleDynamicSelection = (
    sectionLabel: string,
    optionLabel: string,
    optionValue: string,
  ) => {
    const selectedFilter = filters
      .find(group => group.label === sectionLabel)
      ?.values.find(val => val.value === optionValue);

    if (selectedFilter) {
      onToggleFilter({
        label: optionLabel,
        value: selectedFilter.value,
        productVariantIds: selectedFilter.productVariantIds,
      });
    }
  };

  const getStepSize = (min: number, max: number) => {
    const range = max - min;
    if (range > 10000) return 1000;
    if (range > 5000) return 500;
    if (range > 1000) return 100;
    return 50;
  };

  const filterContent = (
    <Box sx={{width: isMobile ? '100vw' : 230, p: 2}}>
      {isMobile && (
        <IconButton onClick={() => setIsDrawerOpen(false)} sx={{mb: 2}}>
          <ArrowLeft />
        </IconButton>
      )}
      <Typography variant="h5" fontWeight="bold" color="primary">
        Filters
      </Typography>

      {filters
        .filter(filter => filter.values && filter.values.length > 0)
        .map(filter => (
          <React.Fragment key={filter.label}>
            <Typography variant="subtitle1" fontWeight="bold" mt={2}>
              {filter.label}
            </Typography>

            {hasSliderMetadata(filter) ? (
              <Box px={1} maxWidth={180} pt={2}>
                <Slider
                  valueLabelDisplay="auto"
                  min={filter.metadata.min}
                  max={filter.metadata.max}
                  step={getStepSize(filter.metadata.min, filter.metadata.max)}
                  value={
                    sliderValues[filter.label] ?? [
                      filter.metadata.min,
                      filter.metadata.max,
                    ]
                  }
                  onChange={(_, newValue) => {
                    setSliderValues(prev => ({
                      ...prev,
                      [filter.label]: newValue as [number, number],
                    }));
                  }}
                  onChangeCommitted={(_, newValue) => {
                    const [min, max] = newValue as [number, number];
                    const sliderFilter = filter.values[0];
                    onToggleFilter({
                      label: filter.label,
                      value: `${min}-${max}`,
                      productVariantIds: sliderFilter.productVariantIds,
                    });
                  }}
                />

                <Box
                  display="flex"
                  justifyContent="space-between"
                  mt={1}
                  gap={1}
                >
                  {(() => {
                    const current = sliderValues[filter.label] ?? [
                      filter.metadata.min,
                      filter.metadata.max,
                    ];
                    const step = getStepSize(
                      filter.metadata.min,
                      filter.metadata.max,
                    );
                    const dropdownOptions: number[] = [];

                    for (
                      let i = filter.metadata.min;
                      i <= filter.metadata.max;
                      i += step
                    ) {
                      dropdownOptions.push(i);
                    }
                    if (
                      dropdownOptions[dropdownOptions.length - 1] !==
                      filter.metadata.max
                    ) {
                      dropdownOptions.push(filter.metadata.max); // Ensure max is included
                    }

                    return (
                      <>
                        <Select
                          size="small"
                          value={current[0]}
                          onChange={e => {
                            const newMin = Number(e.target.value);
                            const updated: [number, number] = [
                              Math.min(newMin, current[1]),
                              Math.max(newMin, current[1]),
                            ];
                            setSliderValues(prev => ({
                              ...prev,
                              [filter.label]: updated,
                            }));
                          }}
                          fullWidth
                          MenuProps={{
                            PaperProps: {
                              style: {
                                maxHeight: 320,
                              },
                            },
                          }}
                        >
                          {dropdownOptions.map(value => (
                            <MenuItem key={value} value={value}>
                              ₹{value}
                            </MenuItem>
                          ))}
                        </Select>

                        <Typography
                          variant="body1"
                          fontWeight="bold"
                          sx={{alignSelf: 'center'}}
                        >
                          to
                        </Typography>

                        <Select
                          size="small"
                          value={current[1]}
                          onChange={e => {
                            const newMax = Number(e.target.value);
                            const updated: [number, number] = [
                              Math.min(current[0], newMax),
                              Math.max(current[0], newMax),
                            ];
                            setSliderValues(prev => ({
                              ...prev,
                              [filter.label]: updated,
                            }));
                          }}
                          fullWidth
                          MenuProps={{
                            PaperProps: {
                              style: {
                                maxHeight: 320,
                              },
                            },
                          }}
                        >
                          {dropdownOptions.map(value => (
                            <MenuItem key={value} value={value}>
                              ₹{value}
                            </MenuItem>
                          ))}
                        </Select>
                      </>
                    );
                  })()}
                </Box>
              </Box>
            ) : (
              <FormGroup>
                {filter.values.map(option => (
                  <FormControlLabel
                    key={option.value}
                    control={
                      <Checkbox
                        checked={isOptionChecked(option.label, option.value)}
                        onChange={() =>
                          handleDynamicSelection(
                            filter.label,
                            option.label,
                            option.value,
                          )
                        }
                        sx={{color: 'navy'}}
                      />
                    }
                    label={option.label}
                  />
                ))}
              </FormGroup>
            )}
          </React.Fragment>
        ))}
    </Box>
  );

  return (
    <>
      {isMobile ? (
        <Button variant="contained" onClick={() => setIsDrawerOpen(true)}>
          Open Filters
        </Button>
      ) : (
        <Card
          sx={{
            width: {xs: 250, md: 280},
            p: 2,
            boxShadow: 1,
            borderRadius: 2,
            backgroundColor: '#edeff2',
          }}
        >
          <CardContent>{filterContent}</CardContent>
        </Card>
      )}
      <Drawer
        open={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        anchor="right"
      >
        {filterContent}
      </Drawer>
    </>
  );
};

export default ProductFilters;
