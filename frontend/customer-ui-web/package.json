{"name": "customer-ui-web", "version": "2.0.0", "author": {"name": "PhoenixCoded", "email": "<EMAIL>", "url": "https://phoenixcoded.net/"}, "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "prettier --write ./src", "docker:build": "docker build -t $(npm pkg get name | tr -d '\"')-$(npm pkg get version | tr -d '\"') ."}, "dependencies": {"@cashfreepayments/cashfree-js": "^1.0.5", "@emotion/cache": "^11.13.1", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@mui/base": "^5.0.0-beta.40", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "^5.16.6", "@mui/system": "^5.16.6", "@reduxjs/toolkit": "^2.7.0", "@svgr/webpack": "^8.1.0", "@types/draft-js": "^0.11.18", "@types/draftjs-to-html": "^0.8.4", "@types/firebase": "^3.2.3", "axios": "^1.7.3", "axios-mock-adapter": "^2.0.0", "chance": "^1.1.13", "country-state-city": "^3.2.1", "dayjs": "^1.11.13", "debounce": "^2.2.0", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "emoji-picker-react": "^4.12.2", "firebase": "^11.10.0", "formik": "^2.4.6", "framer-motion": "^11.3.21", "history": "^5.3.0", "iconsax-react": "^0.0.8", "jotai": "^2.12.5", "lodash": "^4.17.21", "next": "^14.2.5", "notistack": "^3.0.1", "process": "^0.11.10", "react": "^18.3.1", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-intl": "^6.6.8", "react-otp-input": "^3.1.1", "react-redux": "^9.2.0", "react-slick": "^0.30.2", "react-timer-hook": "^3.0.7", "sharp": "^0.34.1", "simplebar-react": "^3.2.6", "styled-jsx": "^5.1.6", "stylis": "^4.3.6", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.2.5", "web-vitals": "^4.2.3", "yup": "^1.4.0"}, "devDependencies": {"@types/chance": "^1.1.6", "@types/lodash": "^4.17.7", "@types/node": "20.11.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-slick": "^0.23.13", "@types/webpack-env": "^1.18.5", "@typescript-eslint/eslint-plugin": "^8.0.1", "@typescript-eslint/parser": "^8.0.1", "encoding": "^0.1.13", "eslint": "^8.57.0", "eslint-config-next": "^14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "immutable": "^4.3.7", "prettier": "^3.3.3", "typescript": "^5.3.3"}}