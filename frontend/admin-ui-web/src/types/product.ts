import { ReactElement, ReactNode } from 'react';
import { FieldType } from './product-dto';
import { Collection } from './collection';
import { SellerList } from './seller';
import { Facet } from './facet';
import { ProductStatus } from 'enums/product-status.enum';

export interface Asset {
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  name: string;
  type: string;
  mimeType: string;
  width: number;
  height: number;
  fileSize: number;
  source: string;
  preview: string;
  focalPoint: string;
  previewUrl: string;
  turnAroundTime?: number;
}

export interface TaxCategory {
  name: string;
  id: string;
  isDefault: boolean;
}

export interface ProductDetail {
  id: string;
  productId: string;
  productVariantId: string;
  details: string;
  foreignKey: string;
}

export interface ProductSpecification {
  id: string;
  name: string;
  value: string;
}

export interface ProductReturnPolicy {
  id: string;
  returnPolicy: string;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}

export interface ProductSuitability {
  id: string;
  suitableFor: string;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}

export interface ProductMoreInfo {
  id: string;
  info: string;
  productId: string;
  productVariantId: string;
  foreignKey: string;
}

export interface ProductDisclaimer {
  id: string;
  disclaimer: string;
  productId: string;
  productVariantId: string;
}

export interface ProductCustomField {
  id: string;
  productId: string;
  productVariantId: string | null;
  name: string;
  label: string;
  placeholder: string;
  fieldType: FieldType;
  isRequired: boolean;
  validationRegex: string | null;
}

export interface ProductVariant {
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  name: string;
  enabled: boolean;
  sku: string;
  outOfStockThreshold: number;
  trackInventory: string;
  productId: string;
  featuredAssetId: string;
  taxCategoryId: string;
  product: Product;
  taxCategory: TaxCategory;
  productSpecifications: ProductSpecification[];
  productDetails: ProductDetail[];
  productMoreInfos: ProductMoreInfo[];
  productDisclaimers: ProductDisclaimer[];
  productReturnPolicies: ProductReturnPolicy[];
  productSuitabilities: ProductSuitability[];
  productTermsAndConditions: ProductTermsAndCondition[];
  productBoxContents: ProductBoxContent[];
  productUniquenesses: ProductUniqueness[];
  productPersonalWorks: ProductPersonalWork[];
  featuredAsset: Asset;
  assets: Asset[];
  productCustomizationFields: ProductCustomField[];
}

export interface ProductTermsAndCondition {
  id: string;
  terms: string;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}

export interface ProductBoxContent {
  id: string;
  itemName: string;
  quantity: number;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}

export interface ProductUniqueness {
  id: string;
  uniqueness: string;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}

export interface ProductPersonalWork {
  id: string;
  workLevel: string;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}
export interface ProductAsset {
  id: string;
  assetId: string;
  productId: string;
  position: number;
  asset: Asset;
}

export interface FacetValue {
  id: string;
  name: string;
  code: string;
  facet: Facet;
}
export interface ProductFacetValue {
  id: string;
  productId: string;
  facetValueId: string;
  facetValue: FacetValue;
}
export interface Product {
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  name: string;
  description: string;
  productId: string;
  slug: string;
  enabled: boolean;
  sellerId: string;
  collectionId: string;
  featuredAssetId: string;
  taxCategory: TaxCategory;
  productSpecifications: ProductSpecification[];
  productMoreInfo: ProductMoreInfo;
  productSuitability: ProductSuitability;
  productTermsAndCondition: ProductTermsAndCondition;
  productBoxContents: ProductBoxContent[];
  featuredAsset: Asset;
  productAssets: ProductAsset[];
  productVariants: ProductVariant[];
  productCustomizationFields: ProductCustomField[];
  collection: Collection;
  seller: SellerList;
  productFacetValues: ProductFacetValue[];
  taxCategoryId: string;
  isGiftWrapAvailable: boolean;
  productDetail: ProductDetail;
  productDisclaimer: ProductDisclaimer;
  productReturnPolicy: ProductReturnPolicy;
  productUniqueness: ProductUniqueness;
  productPersonalWork: ProductPersonalWork;
  isGiftWrapCharge?: number;
  status?: ProductStatus;
  rejectedReason?: string;
  averageWeight?: number;
  turnAroundTime?: number;
}

export interface ProductTabsProps {
  children?: ReactElement | ReactNode | string;
  value: string | number;
  index: number;
}
