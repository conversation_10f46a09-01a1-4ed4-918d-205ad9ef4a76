'use client';

import { useEffect, useState } from 'react';

// material-ui
import { useTheme } from '@mui/material/styles';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Drawer from '@mui/material/Drawer';
import Divider from '@mui/material/Divider';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';

import IconButton from 'components/@extended/IconButton';
import SimpleBar from 'components/third-party/SimpleBar';

// assets
import { Add } from 'iconsax-react';

// types
import { ITicket, TicketStatus } from './index';

interface ItemDetailsProps {
  tickets: ITicket[];
}

// Global state for selected item (in a real app, this would be in context/redux)
let selectedItemId: string | null = null;
let selectedItemListeners: ((id: string | null) => void)[] = [];

export const setSelectedItem = (id: string | null) => {
  selectedItemId = id;
  selectedItemListeners.forEach((listener) => listener(id));
};

export const useSelectedItem = () => {
  const [selectedId, setSelectedId] = useState<string | null>(selectedItemId);

  useEffect(() => {
    const listener = (id: string | null) => setSelectedId(id);
    selectedItemListeners.push(listener);

    return () => {
      selectedItemListeners = selectedItemListeners.filter((l) => l !== listener);
    };
  }, []);

  return selectedId;
};

// ==============================|| KANBAN BOARD - ITEM DETAILS ||============================== //

export default function ItemDetails({ tickets }: ItemDetailsProps) {
  const theme = useTheme();
  const selectedItemId = useSelectedItem();
  const selectedTicket = tickets.find((ticket) => ticket.id === selectedItemId);

  // drawer
  const [open, setOpen] = useState<boolean>(false);
  const handleDrawerOpen = () => {
    setOpen(false);
    setSelectedItem(null);
  };

  useEffect(() => {
    if (selectedItemId) {
      setOpen(true);
    }
  }, [selectedItemId]);

  const getPriorityColor = (priority?: number) => {
    switch (priority) {
      case 1:
        return 'error';
      case 2:
        return 'warning';
      case 3:
        return 'info';
      default:
        return 'default';
    }
  };

  const getPriorityText = (priority?: number) => {
    switch (priority) {
      case 1:
        return 'High';
      case 2:
        return 'Medium';
      case 3:
        return 'Low';
      default:
        return 'None';
    }
  };

  const getStatusColor = (status: TicketStatus) => {
    switch (status) {
      case TicketStatus.Open:
        return 'primary';
      case TicketStatus.InProgress:
        return 'warning';
      case TicketStatus.Resolved:
        return 'success';
      case TicketStatus.Closed:
        return 'default';
      default:
        return 'default';
    }
  };

  return (
    <Drawer
      sx={{
        ml: open ? 3 : 0,
        flexShrink: 0,
        zIndex: 1200,
        overflowX: 'hidden',
        width: { xs: 320, md: 450 },
        '& .MuiDrawer-paper': {
          width: { xs: 320, md: 450 },
          border: 'none',
          borderRadius: '0px'
        }
      }}
      variant="temporary"
      anchor="right"
      open={open}
      ModalProps={{ keepMounted: true }}
      onClose={handleDrawerOpen}
    >
      {open && (
        <SimpleBar sx={{ overflowX: 'hidden', height: '100vh' }}>
          {selectedTicket ? (
            <>
              <Box sx={{ p: 3 }}>
                <Grid container alignItems="center" spacing={0.5} justifyContent="space-between">
                  <Grid item sx={{ width: 'calc(100% - 64px)' }}>
                    <Stack direction="row" spacing={0.5} alignItems="center" justifyContent="space-between">
                      <Typography
                        variant="h4"
                        sx={{
                          display: 'inline-block',
                          width: 'calc(100% - 34px)',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          verticalAlign: 'middle'
                        }}
                      >
                        {selectedTicket.title}
                      </Typography>
                    </Stack>
                  </Grid>

                  <Grid item>
                    <Stack direction="row" alignItems="center">
                      <Tooltip title="Close">
                        <IconButton color="secondary" onClick={handleDrawerOpen} size="small" sx={{ fontSize: '0.875rem' }}>
                          <Add style={{ transform: 'rotate(45deg)' }} />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </Grid>
                </Grid>
              </Box>
              <Divider />

              <Box sx={{ p: 3 }}>
                <Stack spacing={3}>
                  {/* Short Code */}
                  <Box>
                    <Typography variant="h6" color="textSecondary" gutterBottom>
                      Ticket ID
                    </Typography>
                    <Typography variant="body1" fontWeight="medium">
                      {selectedTicket.shortCode}
                    </Typography>
                  </Box>

                  {/* Status */}
                  <Box>
                    <Typography variant="h6" color="textSecondary" gutterBottom>
                      Status
                    </Typography>
                    <Chip label={selectedTicket.status} color={getStatusColor(selectedTicket.status)} size="small" />
                  </Box>

                  {/* Priority */}
                  <Box>
                    <Typography variant="h6" color="textSecondary" gutterBottom>
                      Priority
                    </Typography>
                    <Chip label={getPriorityText(selectedTicket.priority)} color={getPriorityColor(selectedTicket.priority)} size="small" />
                  </Box>

                  {/* Category */}
                  <Box>
                    <Typography variant="h6" color="textSecondary" gutterBottom>
                      Category
                    </Typography>
                    <Typography variant="body1">{selectedTicket.category}</Typography>
                  </Box>

                  {/* Description */}
                  <Box>
                    <Typography variant="h6" color="textSecondary" gutterBottom>
                      Description
                    </Typography>
                    <Typography variant="body1">{selectedTicket.description}</Typography>
                  </Box>

                  {/* Attachments */}
                  {selectedTicket?.attachments && (
                    <Box>
                      <Typography variant="h6" color="textSecondary" gutterBottom>
                        Attachment
                      </Typography>
                      <Box
                        component="img"
                        src={selectedTicket.attachments}
                        alt="Ticket attachment"
                        sx={{
                          width: '100%',
                          maxHeight: 300,
                          objectFit: 'contain',
                          borderRadius: 1,
                          border: `1px solid ${theme.palette.divider}`,
                          cursor: 'pointer',
                          '&:hover': {
                            opacity: 0.8
                          }
                        }}
                        onClick={() => {
                          // Open attachment in new tab/window for full view
                          const newWindow = window.open();
                          if (newWindow) {
                            newWindow.document.write(`
                              <html>
                                <head><title>Attachment - ${selectedTicket.title}</title></head>
                                <body style="margin:0;display:flex;justify-content:center;align-items:center;min-height:100vh;background:#f5f5f5;">
                                  <img src="${selectedTicket.attachments}" style="max-width:100%;max-height:100%;object-fit:contain;" />
                                </body>
                              </html>
                            `);
                          }
                        }}
                      />
                    </Box>
                  )}

                  {/* Created Information */}
                  <Box>
                    <Typography variant="h6" color="textSecondary" gutterBottom>
                      Created
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {selectedTicket.createdOn ? new Date(selectedTicket.createdOn).toLocaleDateString() : 'N/A'}
                      {selectedTicket.createdBy && ` by ${selectedTicket.createdBy}`}
                    </Typography>
                  </Box>

                  {/* Modified Information */}
                  {selectedTicket.modifiedOn && (
                    <Box>
                      <Typography variant="h6" color="textSecondary" gutterBottom>
                        Last Modified
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {new Date(selectedTicket.modifiedOn).toLocaleDateString()}
                        {selectedTicket.modifiedBy && ` by ${selectedTicket.modifiedBy}`}
                      </Typography>
                    </Box>
                  )}
                </Stack>
              </Box>
            </>
          ) : (
            <Stack justifyContent="center" alignItems="center" sx={{ height: '100vh' }}>
              <Typography variant="h5" color="error">
                No ticket found
              </Typography>
            </Stack>
          )}
        </SimpleBar>
      )}
    </Drawer>
  );
}
