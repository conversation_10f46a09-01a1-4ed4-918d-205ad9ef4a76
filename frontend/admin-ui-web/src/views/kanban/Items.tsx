'use client';

import { useState, CSSProperties, MouseEvent } from 'react';

// material-ui
import { useTheme, Theme } from '@mui/material/styles';
import Menu from '@mui/material/Menu';
import Stack from '@mui/material/Stack';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';
import Box from '@mui/material/Box';

// third-party
import { Draggable, DraggingStyle, NotDraggingStyle } from '@hello-pangea/dnd';

// project-imports
import IconButton from 'components/@extended/IconButton';
import MoreIcon from 'components/@extended/MoreIcon';
import { setSelectedItem } from './ItemDetails';

// assets
import { Calendar, AttachSquare } from 'iconsax-react';

// types
import { ITicket /* TicketStatus */ } from './index';

interface Props {
  ticket: ITicket;
  index: number;
}

// item drag wrapper
function getDragWrapper(
  isDragging: boolean,
  draggableStyle: DraggingStyle | NotDraggingStyle | undefined,
  theme: Theme,
  radius: string
): CSSProperties | undefined {
  const bgcolor = theme.palette.background.paper + '99';
  return {
    userSelect: 'none',
    margin: `0 0 ${8}px 0`,
    padding: 16,
    border: '1px solid',
    borderColor: theme.palette.divider,
    backgroundColor: isDragging ? bgcolor : theme.palette.background.paper,
    borderRadius: radius,
    ...draggableStyle
  };
}

// ==============================|| KANBAN BOARD - ITEMS ||============================== //

export default function Items({ ticket, index }: Props) {
  const theme = useTheme();

  const handlerDetails = (id: string) => {
    setSelectedItem(id);
  };

  const [anchorEl, setAnchorEl] = useState<Element | (() => Element) | null | undefined>();
  const handleClick = (event: MouseEvent<HTMLButtonElement> | undefined) => {
    setAnchorEl(event?.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const getPriorityColor = (priority?: number) => {
    switch (priority) {
      case 1:
        return 'error';
      case 2:
        return 'warning';
      case 3:
        return 'info';
      default:
        return 'default';
    }
  };

  const getPriorityText = (priority?: number) => {
    switch (priority) {
      case 1:
        return 'High';
      case 2:
        return 'Medium';
      case 3:
        return 'Low';
      default:
        return 'None';
    }
  };

  // const getStatusColor = (status: TicketStatus) => {
  //   switch (status) {
  //     case TicketStatus.Open:
  //       return 'primary';
  //     case TicketStatus.InProgress:
  //       return 'warning';
  //     case TicketStatus.Resolved:
  //       return 'success';
  //     case TicketStatus.Closed:
  //       return 'default';
  //     default:
  //       return 'default';
  //   }
  // };

  if (!ticket?.id) return null;

  return (
    <Draggable key={ticket.id} draggableId={ticket.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          style={getDragWrapper(snapshot.isDragging, provided.draggableProps.style, theme, `12px`)}
        >
          <Stack spacing={2}>
            {/* Header with title and more button */}
            <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
              <Typography
                onClick={() => handlerDetails(ticket.id!)}
                variant="subtitle1"
                sx={{
                  display: 'inline-block',
                  width: 'calc(100% - 34px)',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  verticalAlign: 'middle',
                  cursor: 'pointer',
                  '&:hover': {
                    textDecoration: 'underline'
                  }
                }}
              >
                {ticket.title}
              </Typography>

              <IconButton size="small" color="secondary" onClick={handleClick} aria-controls="menu-comment" aria-haspopup="true">
                <MoreIcon />
              </IconButton>
            </Stack>

            {/* Short Code */}
            <Box>
              <Typography variant="body2" color="textSecondary">
                {ticket.shortCode}
              </Typography>
            </Box>

            {/* Priority and Category */}
            <Stack direction="row" spacing={1} alignItems="center">
              <Chip label={getPriorityText(ticket.priority)} color={getPriorityColor(ticket.priority)} size="small" />
              <Chip label={ticket.category} variant="outlined" size="small" />
            </Stack>

            {/* Created Date */}
            {ticket.createdOn && (
              <Stack direction="row" spacing={1} alignItems="center">
                <Calendar size={16} color={theme.palette.text.secondary} />
                <Typography variant="body2" color="textSecondary">
                  {new Date(ticket.createdOn).toLocaleDateString()}
                </Typography>
              </Stack>
            )}

            {/* Description preview */}
            {ticket.description && (
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}
              >
                {ticket.description}
              </Typography>
            )}

            {/* Attachments */}
            {ticket?.attachments && (
              <Box>
                <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                  <AttachSquare size={16} color={theme.palette.text.secondary} />
                  <Typography variant="body2" color="textSecondary">
                    Attachment
                  </Typography>
                </Stack>
                <Box
                  component="img"
                  src={ticket.attachments}
                  alt="Ticket attachment"
                  sx={{
                    width: '100%',
                    maxHeight: 120,
                    objectFit: 'cover',
                    borderRadius: 1,
                    border: `1px solid ${theme.palette.divider}`,
                    cursor: 'pointer',
                    '&:hover': {
                      opacity: 0.8
                    }
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    // Open attachment in new tab/window
                    const newWindow = window.open();
                    if (newWindow) {
                      newWindow.document.write(`
                        <html>
                          <head><title>Attachment</title></head>
                          <body style="margin:0;display:flex;justify-content:center;align-items:center;min-height:100vh;background:#f5f5f5;">
                            <img src="${ticket.attachments}" style="max-width:100%;max-height:100%;object-fit:contain;" />
                          </body>
                        </html>
                      `);
                    }
                  }}
                />
              </Box>
            )}

            {/* Menu */}
            <Menu
              id="menu-comment"
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl)}
              onClose={handleClose}
              variant="selectedMenu"
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right'
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right'
              }}
            >
              <MenuItem
                onClick={() => {
                  handleClose();
                  handlerDetails(ticket.id!);
                }}
              >
                View Details
              </MenuItem>
            </Menu>
          </Stack>
        </div>
      )}
    </Draggable>
  );
}
